{"root": [{"assemblyName": "ACTk.Runtime", "nameSpace": "CodeStage.AntiCheat.Common", "className": "ACTk", "methodName": "GetUnityInfo", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "ACTk.Runtime", "nameSpace": "CodeStage.AntiCheat.Common", "className": "ContainerHolder", "methodName": "AfterAssembliesLoaded", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "ACTk.Runtime", "nameSpace": "CodeStage.AntiCheat.Common", "className": "ContainerHolder", "methodName": "BeforeSplashScreen", "loadTypes": 3, "isUnityClass": false}, {"assemblyName": "ACTk.Runtime", "nameSpace": "CodeStage.AntiCheat.Common", "className": "ContainerHolder", "methodName": "BeforeSceneLoad", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Megagon.MirrorImplementation", "nameSpace": "Mirror", "className": "GeneratedNetworkCode", "methodName": "InitReadWriters", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Megagon.Utility", "nameSpace": "Megagon.Utility.DependencyInjection", "className": "DependencyInjector", "methodName": "OnAfterAssembliesLoaded", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "Megagon.Utility", "nameSpace": "Megagon.Utility.App", "className": "ApplicationQuitManager", "methodName": "Init", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Mirror.Authenticators", "nameSpace": "Mirror", "className": "GeneratedNetworkCode", "methodName": "InitReadWriters", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Mirror.Components", "nameSpace": "Mirror", "className": "GeneratedNetworkCode", "methodName": "InitReadWriters", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Mirror", "nameSpace": "Mirror", "className": "NetworkClient", "methodName": "Shutdown", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Mirror", "nameSpace": "Mirror", "className": "NetworkDiagnostics", "methodName": "ResetStatics", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Mirror", "nameSpace": "Mirror", "className": "NetworkIdentity", "methodName": "ResetStatics", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Mirror", "nameSpace": "Mirror", "className": "NetworkLoop", "methodName": "ResetStatics", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Mirror", "nameSpace": "Mirror", "className": "NetworkLoop", "methodName": "RuntimeInitializeOnLoad", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Mirror", "nameSpace": "Mirror", "className": "NetworkManager", "methodName": "ResetStatics", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Mirror", "nameSpace": "Mirror", "className": "NetworkServer", "methodName": "Shutdown", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Mirror", "nameSpace": "Mirror", "className": "NetworkTime", "methodName": "ResetStatics", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Mirror", "nameSpace": "Mirror", "className": "GeneratedNetworkCode", "methodName": "InitReadWriters", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Mirror.Examples", "nameSpace": "Mirror.Examples.MultipleMatch", "className": "CanvasController", "methodName": "ResetStatics", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Mirror.Examples", "nameSpace": "Mirror.Examples.Chat", "className": "Player", "methodName": "ResetStatics", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Mirror.Examples", "nameSpace": "Mirror", "className": "GeneratedNetworkCode", "methodName": "InitReadWriters", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "ParadoxNotion", "nameSpace": "ParadoxNotion.Services", "className": "MonoManager", "methodName": "Purge", "loadTypes": 4, "isUnityClass": false}, {"assemblyName": "ParadoxNotion", "nameSpace": "ParadoxNotion.Serialization", "className": "JSONSerializer", "methodName": "__FlushDataCache", "loadTypes": 4, "isUnityClass": false}, {"assemblyName": "Platform.PC", "nameSpace": "Platform.PC", "className": "DiscordService", "methodName": "InitOnPlayMode", "loadTypes": 4, "isUnityClass": false}, {"assemblyName": "UniTask", "nameSpace": "Cysharp.Threading.Tasks", "className": "Player<PERSON>oop<PERSON>el<PERSON>", "methodName": "Init", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Unity.MemoryProfiler", "nameSpace": "Unity.MemoryProfiler", "className": "MetadataInjector", "methodName": "PlayerInitMetadata", "loadTypes": 3, "isUnityClass": true}, {"assemblyName": "Unity.ResourceManager", "nameSpace": "UnityEngine.ResourceManagement.ResourceProviders", "className": "AssetBundleProvider", "methodName": "Init", "loadTypes": 4, "isUnityClass": true}]}