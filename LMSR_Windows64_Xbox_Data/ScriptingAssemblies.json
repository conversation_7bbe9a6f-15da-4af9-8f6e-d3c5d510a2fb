{"names": ["UnityEngine.dll", "UnityEngine.AIModule.dll", "UnityEngine.ARModule.dll", "UnityEngine.AccessibilityModule.dll", "UnityEngine.AndroidJNIModule.dll", "UnityEngine.AnimationModule.dll", "UnityEngine.AssetBundleModule.dll", "UnityEngine.AudioModule.dll", "UnityEngine.ClothModule.dll", "UnityEngine.ClusterInputModule.dll", "UnityEngine.ClusterRendererModule.dll", "UnityEngine.ContentLoadModule.dll", "UnityEngine.CoreModule.dll", "UnityEngine.CrashReportingModule.dll", "UnityEngine.DSPGraphModule.dll", "UnityEngine.DirectorModule.dll", "UnityEngine.GIModule.dll", "UnityEngine.GameCenterModule.dll", "UnityEngine.GridModule.dll", "UnityEngine.HotReloadModule.dll", "UnityEngine.IMGUIModule.dll", "UnityEngine.ImageConversionModule.dll", "UnityEngine.InputModule.dll", "UnityEngine.InputLegacyModule.dll", "UnityEngine.JSONSerializeModule.dll", "UnityEngine.LocalizationModule.dll", "UnityEngine.NVIDIAModule.dll", "UnityEngine.ParticleSystemModule.dll", "UnityEngine.PerformanceReportingModule.dll", "UnityEngine.PhysicsModule.dll", "UnityEngine.Physics2DModule.dll", "UnityEngine.ProfilerModule.dll", "UnityEngine.PropertiesModule.dll", "UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll", "UnityEngine.ScreenCaptureModule.dll", "UnityEngine.SharedInternalsModule.dll", "UnityEngine.SpriteMaskModule.dll", "UnityEngine.SpriteShapeModule.dll", "UnityEngine.StreamingModule.dll", "UnityEngine.SubstanceModule.dll", "UnityEngine.SubsystemsModule.dll", "UnityEngine.TLSModule.dll", "UnityEngine.TerrainModule.dll", "UnityEngine.TerrainPhysicsModule.dll", "UnityEngine.TextCoreFontEngineModule.dll", "UnityEngine.TextCoreTextEngineModule.dll", "UnityEngine.TextRenderingModule.dll", "UnityEngine.TilemapModule.dll", "UnityEngine.UIModule.dll", "UnityEngine.UIElementsModule.dll", "UnityEngine.UmbraModule.dll", "UnityEngine.UnityAnalyticsModule.dll", "UnityEngine.UnityAnalyticsCommonModule.dll", "UnityEngine.UnityConnectModule.dll", "UnityEngine.UnityCurlModule.dll", "UnityEngine.UnityTestProtocolModule.dll", "UnityEngine.UnityWebRequestModule.dll", "UnityEngine.UnityWebRequestAssetBundleModule.dll", "UnityEngine.UnityWebRequestAudioModule.dll", "UnityEngine.UnityWebRequestTextureModule.dll", "UnityEngine.UnityWebRequestWWWModule.dll", "UnityEngine.VFXModule.dll", "UnityEngine.VRModule.dll", "UnityEngine.VehiclesModule.dll", "UnityEngine.VideoModule.dll", "UnityEngine.VirtualTexturingModule.dll", "UnityEngine.WindModule.dll", "UnityEngine.XRModule.dll", "Assembly-CSharp.dll", "com.rlabrecque.steamworks.net.dll", "ShapesRuntime.dll", "Megagon.LevelPipeline.Interface.dll", "Unity.ScriptableBuildPipeline.dll", "Megagon.Utility.Interface.Audio.dll", "Mirror.Examples.dll", "RTG.dll", "Megagon.Utility.Interface.dll", "Megagon.LevelPipeline.Draft.Interface.dll", "Megagon.Utility.PropertyGrid.Attributes.dll", "Unity.ProBuilder.Poly2Tri.dll", "FMODUnity.dll", "Unity.TextMeshPro.dll", "Unity.Recorder.dll", "Megagon.Factories.dll", "Megagon.NodeCanvasSupport.dll", "Unity.SharpZipLib.Utils.dll", "Megagon.Utility.PropertyGrid.Interfaces.dll", "Megagon.Utility.FMOD.dll", "Megagon.Networking.dll", "Unity.MemoryProfiler.dll", "Discord.dll", "Unity.Burst.dll", "Megagon.Utility.Interface.Types.Common.dll", "Unity.ProBuilder.dll", "Unity.Recorder.Base.dll", "UniTask.Linq.dll", "Megagon.Utility.UI.Interface.dll", "Unity.Microsoft.GDK.dll", "Megagon.Networking.LeaderboardAdministration.dll", "Megagon.UnsafeFramework.dll", "Megagon.MirrorImplementation.dll", "Rewired_Windows_Functions.dll", "Megagon.Utility.PropertyGrid.dll", "Unity.Animation.Rigging.DocCodeExamples.dll", "Megagon.GameState.dll", "Megagon.Social.Playfab.Transport.dll", "Megagon.Social.dll", "Unity.Addressables.dll", "Mirror.dll", "Megagon.LevelPipeline.Architect.dll", "ACTk.Runtime.dll", "Megagon.Utility.Storage.dll", "Megagon.LevelPipeline.Published.dll", "Megagon.Tracking.dll", "Unity.Postprocessing.Runtime.dll", "Unity.ProBuilder.Stl.dll", "Unity.AI.Navigation.dll", "Megagon.LevelPipeline.dll", "Unity.ResourceManager.dll", "SimpleWebTransport.dll", "Megagon.SnowRiders.dll", "Megagon.Input.dll", "Mirror.Components.dll", "Megagon.UGC.dll", "kcp2k.dll", "Unity.InternalAPIEngineBridge.013.dll", "Unity.ProBuilder.Csg.dll", "Megagon.SnowRiders.Customization.dll", "Mirror.Authenticators.dll", "Megagon.LevelPipeline.Draft.dll", "ParadoxNotion.dll", "NaughtyAttributes.Test.dll", "AmplifyImposters.Runtime.dll", "Megagon.UnitTests.TestUtility.dll", "<PERSON><PERSON><PERSON><PERSON>.BetterShaders.Samples.dll", "Megagon.SnowRiders.EntitledContent.dll", "ZString.dll", "Unity.Animation.Rigging.dll", "Unity.Profiling.Core.dll", "where-allocations.dll", "Megagon.Utility.Encryption.dll", "Megagon.Utility.UI.dll", "Megagon.Utility.dll", "Megagon.Interface.dll", "Platform.Interface.dll", "Megagon.Tracking.Visualization.dll", "Platform.XboxSeries.dll", "UniTask.TextMeshPro.dll", "UniTask.DOTween.dll", "Unity.TerrainTools.dll", "UniTask.Addressables.dll", "Unity.Localization.dll", "InfiniteScrollView.dll", "PlayFab.dll", "Megagon.GameStatistics.dll", "UnityEngine.UI.dll", "NaughtyAttributes.Core.dll", "FMODUnityResonance.dll", "PlayfabPartySdk.dll", "Proxima.dll", "Unity.Timeline.dll", "PlayFabMultiplayer.dll", "Megagon.Utility.AntiCheat.dll", "PlayFabMultiplayerSDK.dll", "Telepathy.dll", "Battlehub.RTEditor.dll", "AzureRESTClient.dll", "Unity.ProBuilder.KdTree.dll", "Rewired.dll", "Unity.Mathematics.dll", "Platform.PC.dll", "UniTask.dll", "Unity.ProGrids.dll", "Unity.Microsoft.GDK.Tools.dll", "Megagon.LevelPipeline.Base.dll", "Google.FlatBuffers.dll", "XblPCSandbox.dll", "Mystery Squiggle.dll", "Unity.SharpZipLib.dll", "NSubstitute.dll", "DemiLib.dll", "DOTween.dll", "Rewired_Windows.dll", "Mystery Squiggle UI.dll", "Rewired_Core.dll", "System.Runtime.CompilerServices.Unsafe.dll", "Unity.Burst.Unsafe.dll", "DOTweenPro.dll", "FlatSharp.Runtime.dll", "Mystery Node Set.dll", "proxima-websocket-sharp.dll", "Newtonsoft.Json.dll", "Castle.Core.dll"], "types": [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16]}