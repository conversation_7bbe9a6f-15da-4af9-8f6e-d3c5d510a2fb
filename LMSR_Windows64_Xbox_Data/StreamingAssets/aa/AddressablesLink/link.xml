<linker>
  <assembly fullname="AmplifyImposters.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="AmplifyImpostors.AmplifyImpostorBakePreset" preserve="all" />
    <type fullname="AmplifyImpostors.TextureOutput" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Assembly-CSharp, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="EmergencyFlare_Light" preserve="all" />
  </assembly>
  <assembly fullname="FMODUnity, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="FMODUnity.StudioEventEmitter" preserve="all" />
    <type fullname="FMODUnity.StudioListener" preserve="all" />
    <type fullname="FMOD.GUID" preserve="nothing" serialized="true" />
    <type fullname="FMODUnity.EventReference" preserve="nothing" serialized="true" />
    <type fullname="FMODUnity.ParamRef" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="InfiniteScrollView, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="HowTungTung.VerticalInfiniteScrollView" preserve="all" />
  </assembly>
  <assembly fullname="Megagon.Factories, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Megagon.Factories.Input.LocalPlayerInputFactory" preserve="all" />
    <type fullname="Megagon.Factories.Input.RemotePlayerInputFactory" preserve="all" />
    <type fullname="Megagon.Factories.Input.ReplayPlayerInputFactory" preserve="all" />
    <type fullname="Megagon.Factories.PlayerFactories.PlayerFactoryAssets" preserve="all" />
  </assembly>
  <assembly fullname="Megagon.Interface, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Megagon.Interface.SnowRiders.LobbyType.QuickmatchStartBalancingData" preserve="all" />
    <type fullname="Megagon.Interface.Types.Balancing.PlayerLevelBalancingData" preserve="all" />
    <type fullname="Megagon.Interface.Types.CurrentGameModeVariable" preserve="all" />
    <type fullname="Megagon.Interface.Types.Gameplay.GameFeedTextSource" preserve="all" />
    <type fullname="Megagon.Interface.Types.GameState.PlayFabBaseConfigurationManager" preserve="all" />
    <type fullname="Megagon.Interface.Types.LobbyTypeVariable" preserve="all" />
    <type fullname="Megagon.Interface.Types.Network.PlayerPurposeVariable" preserve="all" />
    <type fullname="Megagon.Interface.Types.Network.PlayerRoleVariable" preserve="all" />
    <type fullname="Megagon.Interface.Types.PlayerDataVariable" preserve="all" />
    <type fullname="Megagon.Interface.Types.Players.BodyPartTransform" preserve="all" />
    <type fullname="Megagon.Interface.Types.RuntimeDictionaryIInputReader" preserve="all" />
    <type fullname="Megagon.Interface.Types.RuntimeListGameSessionData" preserve="all" />
    <type fullname="Megagon.Interface.Types.Statistics.GameSessionStatisticsScriptableObject" preserve="all" />
    <type fullname="Megagon.Interface.Types.Status.GameSessionStatusScriptableObject" preserve="all" />
    <type fullname="Megagon.Interface.SnowRiders.Content.LevelConfigInfo" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Interface.SnowRiders.LobbyType.PlayerAmountAndWaitingTime" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Interface.Types.Balancing.LevelDefinition" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Interface.SnowRiders.UI.Settings.ColorSchemeEntry" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Interface.SnowRiders.Vehicle.SportTypeUIData" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Interface.Types.GameState.PlayFabBaseConfiguration" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Interface.Types.GameState.VersionAndConfigurationString" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Interface.Types.Customization.ColorSlot" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Interface.Types.Customization.ItemMeshIdentifier" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Interface.Types.Customization.ItemSlotLimitation" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Interface.Types.Customization.VehicleMesh" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Interface.Types.Customization.VehicleMeshIdentifier" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Megagon.LevelPipeline, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Megagon.LevelPipeline.Culling.CullingCamera" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Culling.CullingConfiguration" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Culling.CullingObserver" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Culling.ShadowCullingCamera" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Helpers.ReflectionCamera" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Particles.ParticlePointSpawnShape" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Particles.ParticlesConfiguration" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Particles.ParticleSpawnerTemplate" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Particles.ParticleSphereSpawnShape" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Particles.UnityParticleSpawnerBehaviour" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Water.WaterAudioSourceController" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Particles.UnityParticleSpawnerBehaviour/SystemLink" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Megagon.LevelPipeline.Architect, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Megagon.LevelPipeline.Architect.Analysis.ColliderMetaViewConfiguration" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Analysis.MetaViewCollection" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Analysis.MetaViewController" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Analysis.PlayerPreviewMetaViewConfiguration" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Analysis.ShaderMetaViewConfiguration" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Analysis.TemporaryCheckpointController" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Analysis.TerrainMetaViewConfiguration" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Gizmos.GizmoController" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Graphics.SelectionOutlineEffect" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Graphics.SelectionOutlineRenderSource" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.LevelArchitect" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.LevelManagerHelper" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Playmode.UI.PlaymodeSettingsUI" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Effects.EffectsEditorTool" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Effects.EffectsGlobalSubTool" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Effects.EffectsSkyboxSubTool" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Effects.EffectsVolumeSubTool" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.General.GeneralCameraSettingsTool" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.General.GeneralEditorTool" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.General.GeneralPersistenceSettingsTool" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.General.InputController" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.General.PersistenceOptions" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Particles.ParticlesEditorTool" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Particles.ParticlesPlacementSubTool" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Particles.ParticlesTransformSubTool" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Props.PropsEditorTool" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Props.PropsEraseSubTool" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Props.PropsObstacleSubTool" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Props.PropsPainterSubTool" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Props.PropsPrecisionSubTool" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Props.PropsSelectSubTool" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Props.PropsTransformSubTool" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Route.DeathTriggerVolumeTool" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Route.RouteCheckpointSubTool" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Route.RouteClimbingGearSubTool" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Route.RouteConnectionSubTool" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Route.RouteEditorTool" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Route.RouteInWorldTextSubTool" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Route.RouteRestingSpotSubTool" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Terrain.Brushes.Collections.BrushDescriptorCollection" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Terrain.Brushes.Collections.SnowAndIceBrushOperationCollection" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Terrain.Brushes.Collections.TerraformingBrushOperationCollection" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Terrain.Brushes.Collections.TextureBrushOperationCollection" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Terrain.Brushes.Snow.IceBrushOperation" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Terrain.Brushes.Snow.SnowBrushOperation" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Terrain.Brushes.Splat.TextureBrushOperation" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Terrain.Brushes.Terraforming.HeightAddSubBrushOperation" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Terrain.Brushes.Terraforming.HeightNoiseBrushOperation" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Terrain.Brushes.Terraforming.HeightSetBrushOperation" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Terrain.Brushes.Terraforming.HeightSmoothBrushOperation" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Terrain.Brushes.Terraforming.HoleBrushOperation" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Terrain.TerrainEditorTool" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Terrain.Tools.TerrainChunkSubTool" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Terrain.Tools.TerrainElevationSubTool" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Terrain.Tools.TerrainHeightShiftSubTool" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Terrain.Tools.TerrainSnowSubTool" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Terrain.Tools.TerrainSplineSubTool" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Terrain.Tools.TerrainTextureSubTool" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.View.CameraGeneralOptionsTool" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.View.CameraOptionsTool" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.View.CameraVolumeOptionsTool" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Water.WaterLakeTool" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Water.WaterRiverTool" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Water.WaterTool" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.UI.Analysis.MetaViewAlertUI" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.UI.Analysis.MetaViewDropdown" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.UI.Analysis.TemporaryCheckpointToggle" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.UI.BusyView" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.UI.Content.ContentBundleUpdateItem" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.UI.Content.ContentWindow" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.UI.EditorUICameraLinker" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.UI.Fields.BiomeSelectionField" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.UI.Fields.BiomeSelectionItem" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.UI.Gizmos.GizmoPivotToggle" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.UI.Gizmos.GizmoSpaceToggle" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.UI.Gizmos.ParticleModeToggle" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.UI.LevelArchitectToolboxUI" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.UI.LevelUI" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.UI.MarqueeSelectionUI" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.UI.PlaymodeControlUI" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.UI.Popup.ConfirmationPopup" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.UI.PropertyGrid.PlaymodeGearDropdown" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.UI.PropertyGrid.PlaymodeRouteVersionDropdown" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.UI.Save.SaveListItemUI" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.UI.Save.SaveListUI" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.UI.Save.SaveRouter" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.UI.Save.TopbarUI" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.UI.Startup.CreateNewLevelDisplay" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.UI.Startup.StartUpDisplay" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.UI.Structure.PropInstanceListView" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.UI.Structure.PropInstanceView" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.UI.Undo.UndoRedoHistoryUI" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.UI.Undo.UndoRedoStepUI" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.UI.Undo.UndoRedoUIHook" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.UI.Utils.EffectsToggle" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.UI.Utils.FocusCameraButton" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.UI.Utils.TrackedStepUI" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.UI.Window.WindowButton" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.UI.Window.WindowUI" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Utils.PlayerPreview" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.View.EditorCameraOptions" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.View.FocusCamera" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Particles.EditModeItem" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Particles.SpawnShapeItem" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Particles.TriggerBehaviourItem" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Props.Utils.CoverageModeItem" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Props.Utils.FlipModeItem" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Props.Utils.LayerModeItem" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Props.Utils.MirrorModeItem" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Props.Utils.OrientationModeItem" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Props.Utils.OverlapModeItem" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Props.Utils.SelectionModeItem" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Route.Utils.CheckpointColliderMode" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Route.Utils.RouteVersionItem" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Terrain.Brushes.BrushDescriptor" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Terrain.Brushes.Snow.IcePaintMode" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Water.PainterTools.WaterFlowPainter" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Water.PainterTools.WaterSingleValuePainter" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Architect.Tools.Water.Utils.WaterStateSelectorItem" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Architect.UI.PropertyGrid.BoolSelectorItem" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Architect.Utils.ActivationRuleItem" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Architect.View.EditorCameraOptions/LookSettings" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Architect.View.EditorCameraOptions/MoveSettings" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Architect.View.EditorCameraOptions/OrbitSettings" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Architect.View.EditorCameraOptions/PanSettings" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Architect.View.EditorCameraOptions/ZoomSettings" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Architect.View.FocusCameraHotkeys" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Megagon.LevelPipeline.Base, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Megagon.LevelPipeline.Base.Props.PropImpactTrigger" preserve="all" />
  </assembly>
  <assembly fullname="Megagon.LevelPipeline.Draft, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Megagon.LevelPipeline.Draft.PropertyGrid.Fields.CheckpointConnectionListField" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Draft.PropertyGrid.Fields.CheckpointMultiSelectionField" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Draft.PropertyGrid.Fields.CheckpointMultiSelectionItem" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Draft.PropertyGrid.Fields.ColorTemplateSelectorField" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Draft.PropertyGrid.Fields.EffectOverrideGroupField" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Draft.PropertyGrid.Fields.EffectPresetsField" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Draft.PropertyGrid.Fields.FloatEffectParameterField" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Draft.PropertyGrid.Fields.ObstacleFormationMultiSelectionField" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Draft.PropertyGrid.Fields.Particles.ParticlesTemplatesField" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Draft.PropertyGrid.Fields.Particles.TemplateToggle" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Draft.PropertyGrid.Fields.Props.BiomeToggle" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Draft.PropertyGrid.Fields.Props.FilterSeperator" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Draft.PropertyGrid.Fields.Props.ObstacleFormationMultiSelectionItem" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Draft.PropertyGrid.Fields.Props.ObstacleFormationMutliSelectionItemProp" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Draft.PropertyGrid.Fields.Props.PropCategoryUIData" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Draft.PropertyGrid.Fields.Props.PropDatabaseField" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Draft.PropertyGrid.Fields.Props.PropGroupElement" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Draft.PropertyGrid.Fields.Props.PropGroupingDisplay" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Draft.PropertyGrid.Fields.Props.PropTagToggle" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Draft.PropertyGrid.Fields.Props.PropToggle" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Draft.PropertyGrid.Fields.Props.SnowTypeToggle" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Draft.PropertyGrid.Fields.Route.CheckpointConnectionListFieldItem" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Draft.PropertyGrid.Fields.Route.ObstacleFormationLinkDropdown" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Draft.PropertyGrid.Fields.Route.ObstacleFormationLinkFormationItem" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Draft.PropertyGrid.Fields.Route.ObstacleFormationLinkGroupItem" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Draft.PropertyGrid.Fields.Route.ObstacleFormationLinkItem" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Draft.PropertyGrid.Fields.Route.UIHoverContainer" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Draft.PropertyGrid.Generation.LevelPropertyFieldConfiguration" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Draft.PropertyGrid.UI.LevelPropertyGridViewer" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Draft.PropertyGrid.Fields.Props.PropCategoryUIData/PropCategoryItem" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Megagon.LevelPipeline.Interface, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Megagon.LevelPipeline.Base.Snow.GroundController" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Base.Snow.SnowParticleManager" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Base.Snow.SnowParticleSystemGroup" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Base.Snow.SnowTrailCameraSocket" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Route.GearOverride" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Route.GearRestingSpotTransformDatabase" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Audio.AmbientAudio" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Audio.AudioConfiguration" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Audio.AudioCullingObserver" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Background.BackgroundMesh" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Background.BackgroundQualityConfiguration" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Background.ParallaxBackgroundConfiguration" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Biome.BiomeConfiguration" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Biome.BiomeDefinition" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Effects.EffectsController" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Effects.Lighting.LightEffectsController" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Effects.Lighting.Skybox.SkyboxEffect" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Effects.Lighting.Sun.SunEffect" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Effects.Options.EffectColorCorrectionPreset" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Effects.Options.EffectLightPreset" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Effects.Options.EffectWeatherPreset" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Effects.Options.PostProcessConfiguration" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Effects.PostProcessing.GradingMapping" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Effects.PostProcessing.SunMapping" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Effects.PostProcessing.WeatherMapping" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Effects.Weather.Clouds.CloudsEffect" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Effects.Weather.Lightning.LightningEffect" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Effects.Weather.Precipitation.PrecipitationEffect" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Effects.Weather.Precipitation.PrecipitationMesh" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Effects.Weather.Precipitation.PrecipitationSplashParticles" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Effects.Weather.Precipitation.SnowGPUParticles" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Effects.Weather.Precipitation.SnowParticles" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Effects.Weather.WeatherEffectsController" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Effects.Weather.Wind.WindEffect" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Grounds.GroundQualityConfiguration" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Grounds.GroundType" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Grounds.SnowGroundType" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Persistence.LevelPersistenceConfiguration" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Props.Data.SystemProp" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Props.Imposter.PropImposterBaker" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Props.Physics.PropCollisions" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Props.PropDatabase" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Props.Rendering.Unity.GameObjectVisuals" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Route.RouteConfiguration" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Snow.SnowBall" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Snow.SnowBallSpawner" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Snow.SnowElement" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Terrain.TerrainConfiguration" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Water.Physics.BuoyancyReceiver" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Water.WaterConfiguration" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Water.WaterType" preserve="all" />
    <type fullname="SnowTrailCamera" preserve="all" />
    <type fullname="Megagon.LevelPipeline.Interface.Route.GearStylePrefab" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Base.Snow.GlintsData" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Effects.Weather.Precipitation.SnowGPUParticles/QualitySettings" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Physics.GroundPhysicsProperties" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Physics.GroundSnowProperties" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Props.Imposter.PropImposterBaker/ImposterQualitySettings" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Props.PropDatabase/ShaderTypeData" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Interface.Audio.IAudioConfiguration/PropAudioData" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Interface.Background.IBackgroundQualityConfiguration/QualityTier" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Interface.Culling.ICullingConfiguration/GroupData" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Interface.Culling.ICullingConfiguration/SizeException" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Interface.Effects.EffectOverrideType" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Interface.Grounds.IGroundQualityConfiguration/SnowTier" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Interface.Particles.IParticlesConfiguration/QualitySettings" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Interface.Particles.IParticlesConfiguration/WaterParticleQualitySettings" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Interface.Route.PlayerAndGearPoint" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Interface.Terrain.ITerrainConfiguration/QualitySettings" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Effects.EffectsCloudsQualitySettings" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Effects.EffectsFogQualitySettings" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Effects.EffectsReflectionQualitySettings" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Effects.Options.EffectConfiguration" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Effects.Options.EffectPreset/AtmosphereOverrides" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Effects.Options.EffectPreset/CloudsOverrides" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Effects.Options.EffectPreset/ColorCorrectionOverrides" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Effects.Options.EffectPreset/LightOverrides" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Effects.Options.EffectPreset/Parameter" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Effects.Options.EffectPreset/RainOverrides" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Effects.Options.EffectPreset/SnowfallOverrides" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Effects.Options.EffectPreset/ThunderstormOverrides" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Interface.Types.Effects.Options.EffectPreset/WindOverrides" preserve="nothing" serialized="true" />
    <type fullname="Megagon.LevelPipeline.Interface.Water.IWaterConfiguration/QualitySettings" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Megagon.MirrorImplementation, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Megagon.MirrorImplementation.LMSRNetworkManager" preserve="all" />
    <type fullname="Megagon.MirrorImplementation.NetworkBehaviours.ClimbingGearSyncNetworkBehaviour" preserve="all" />
    <type fullname="Megagon.MirrorImplementation.NetworkBehaviours.EmoteSyncNetworkBehaviour" preserve="all" />
    <type fullname="Megagon.MirrorImplementation.NetworkBehaviours.GameQualitySyncNetworkBehavior" preserve="all" />
    <type fullname="Megagon.MirrorImplementation.NetworkBehaviours.GameSessionDataListSyncBehaviour" preserve="all" />
    <type fullname="Megagon.MirrorImplementation.NetworkBehaviours.GameSessionStatisticsSyncBehaviour" preserve="all" />
    <type fullname="Megagon.MirrorImplementation.NetworkBehaviours.GameSessionStatusSyncBehaviour" preserve="all" />
    <type fullname="Megagon.MirrorImplementation.NetworkBehaviours.LobbyMasterNetworkBehaviour" preserve="all" />
    <type fullname="Megagon.MirrorImplementation.NetworkBehaviours.NetworkGameStateNetworkBehaviour" preserve="all" />
    <type fullname="Megagon.MirrorImplementation.NetworkBehaviours.NetworkPlayerNetworkBehaviour" preserve="all" />
    <type fullname="Megagon.MirrorImplementation.NetworkBehaviours.NetworkSessionNetworkEventBehaviour" preserve="all" />
    <type fullname="Megagon.MirrorImplementation.NetworkBehaviours.PlaceableRespawnPointSyncNetworkBehaviour" preserve="all" />
    <type fullname="Megagon.MirrorImplementation.NetworkBehaviours.RecoverZoneSyncNetworkBehaviour" preserve="all" />
    <type fullname="Megagon.MirrorImplementation.NetworkBehaviours.RestingSpotSyncNetworkBehaviour" preserve="all" />
    <type fullname="Megagon.MirrorImplementation.NetworkBehaviours.RunRewardSyncNetworkBehavior" preserve="all" />
    <type fullname="Megagon.MirrorImplementation.NetworkBehaviours.TeamModeScoreSyncNetworkBehavior" preserve="all" />
  </assembly>
  <assembly fullname="Megagon.SnowRiders, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Megagon.SnowRiders.Achievements.Achievement" preserve="all" />
    <type fullname="Megagon.SnowRiders.Achievements.AchievementsCollection" preserve="all" />
    <type fullname="Megagon.SnowRiders.Achievements.HasCompletedTutorialEvaluator" preserve="all" />
    <type fullname="Megagon.SnowRiders.Achievements.MountainChallengeEvaluator" preserve="all" />
    <type fullname="Megagon.SnowRiders.Achievements.NotPipelineLevelEvaluator" preserve="all" />
    <type fullname="Megagon.SnowRiders.Achievements.TotalBadgesEvaluator" preserve="all" />
    <type fullname="Megagon.SnowRiders.Achievements.TotalRescuesEvaluator" preserve="all" />
    <type fullname="Megagon.SnowRiders.Achievements.UnlockAllGrabsEvaluator" preserve="all" />
    <type fullname="Megagon.SnowRiders.Achievements.UnlockGearCountEvaluator" preserve="all" />
    <type fullname="Megagon.SnowRiders.Achievements.UnlockTrailEvaluator" preserve="all" />
    <type fullname="Megagon.SnowRiders.Animation.BreakableFixedJoint" preserve="all" />
    <type fullname="Megagon.SnowRiders.Animation.Camera.RandomSinMovement" preserve="all" />
    <type fullname="Megagon.SnowRiders.Animation.CustomParentConstraint" preserve="all" />
    <type fullname="Megagon.SnowRiders.Animation.LocalPlayerMenuAnimation" preserve="all" />
    <type fullname="Megagon.SnowRiders.Animation.RandomAnimationSelector" preserve="all" />
    <type fullname="Megagon.SnowRiders.Animation.RestingIdleController" preserve="all" />
    <type fullname="Megagon.SnowRiders.Animation.SkiAnimation" preserve="all" />
    <type fullname="Megagon.SnowRiders.Animation.SkiIK" preserve="all" />
    <type fullname="Megagon.SnowRiders.Animation.SnowboardAnimation" preserve="all" />
    <type fullname="Megagon.SnowRiders.Animation.StateMachineObserver" preserve="all" />
    <type fullname="Megagon.SnowRiders.Audio.AudioListener" preserve="all" />
    <type fullname="Megagon.SnowRiders.Audio.ImpactAudioTrigger" preserve="all" />
    <type fullname="Megagon.SnowRiders.Audio.LocalPlayerAudio" preserve="all" />
    <type fullname="Megagon.SnowRiders.Audio.SpecialAudioEventHandler" preserve="all" />
    <type fullname="Megagon.SnowRiders.Audio.VehicleAudio" preserve="all" />
    <type fullname="Megagon.SnowRiders.Cameras.DOFAdapterGameplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.Cameras.DynamicCamera" preserve="all" />
    <type fullname="Megagon.SnowRiders.Cameras.MenuCamera" preserve="all" />
    <type fullname="Megagon.SnowRiders.Cameras.MenuCameraBehaviour" preserve="all" />
    <type fullname="Megagon.SnowRiders.Cameras.Modifier.CameraModifierSampler" preserve="all" />
    <type fullname="Megagon.SnowRiders.Cameras.PlayCamera" preserve="all" />
    <type fullname="Megagon.SnowRiders.Cameras.Preview.PreviewCamera" preserve="all" />
    <type fullname="Megagon.SnowRiders.Cameras.ScreenShake" preserve="all" />
    <type fullname="Megagon.SnowRiders.Configuration.ActiveConfiguration" preserve="all" />
    <type fullname="Megagon.SnowRiders.Configuration.BaseConfiguration" preserve="all" />
    <type fullname="Megagon.SnowRiders.Configuration.Content.MountainConfiguration" preserve="all" />
    <type fullname="Megagon.SnowRiders.Configuration.GameMode.Racing.EndTimerConfiguration" preserve="all" />
    <type fullname="Megagon.SnowRiders.Configuration.PlatformSubConfigs.XboxPCSubConfig" preserve="all" />
    <type fullname="Megagon.SnowRiders.Configuration.SubConfigurations.ActiveScreensSubConfiguration" preserve="all" />
    <type fullname="Megagon.SnowRiders.Configuration.SubConfigurations.BuildSettingsSubConfiguration" preserve="all" />
    <type fullname="Megagon.SnowRiders.Configuration.SubConfigurations.ContentSettingsSubConfiguration" preserve="all" />
    <type fullname="Megagon.SnowRiders.Configuration.SubConfigurations.GraphicsSubConfiguration" preserve="all" />
    <type fullname="Megagon.SnowRiders.Configuration.SubConfigurations.Helper.Endpoint" preserve="all" />
    <type fullname="Megagon.SnowRiders.Configuration.SubConfigurations.LogSettingsSubConfiguration" preserve="all" />
    <type fullname="Megagon.SnowRiders.Debugging.Network.NetworkInterpolation.TransformValueDuringOneLoopRecordController" preserve="all" />
    <type fullname="Megagon.SnowRiders.Effects.Haptics.HapticsConfiguration" preserve="all" />
    <type fullname="Megagon.SnowRiders.Emotes.Emote" preserve="all" />
    <type fullname="Megagon.SnowRiders.Emotes.EmoteCategory" preserve="all" />
    <type fullname="Megagon.SnowRiders.Emotes.EmoteDatabase" preserve="all" />
    <type fullname="Megagon.SnowRiders.Emotes.EmoteSet" preserve="all" />
    <type fullname="Megagon.SnowRiders.Gameplay.Checkpoint" preserve="all" />
    <type fullname="Megagon.SnowRiders.Gameplay.CheckpointColorHandler" preserve="all" />
    <type fullname="Megagon.SnowRiders.Gameplay.ClimbingGear.ClimbingGearInstance" preserve="all" />
    <type fullname="Megagon.SnowRiders.Gameplay.DebugRespawnPointFinderConfiguration" preserve="all" />
    <type fullname="Megagon.SnowRiders.Gameplay.InWorldTextInstance" preserve="all" />
    <type fullname="Megagon.SnowRiders.Gameplay.MenuRespawnPointFinder" preserve="all" />
    <type fullname="Megagon.SnowRiders.Gameplay.PlaceableRespawnPoint" preserve="all" />
    <type fullname="Megagon.SnowRiders.Gameplay.PlayerRespawnPoint" preserve="all" />
    <type fullname="Megagon.SnowRiders.Gameplay.PlayerRespawnPointGroup" preserve="all" />
    <type fullname="Megagon.SnowRiders.Gameplay.RecoverZone" preserve="all" />
    <type fullname="Megagon.SnowRiders.Gameplay.RespawnPointFinderConfiguration" preserve="all" />
    <type fullname="Megagon.SnowRiders.Gameplay.RestingSpotRespawnPoint" preserve="all" />
    <type fullname="Megagon.SnowRiders.Gameplay.RestingSpots.RestingSpotInstance" preserve="all" />
    <type fullname="Megagon.SnowRiders.Gameplay.Tutorial.TutorialChallengeList" preserve="all" />
    <type fullname="Megagon.SnowRiders.Gameplay.Tutorial.TutorialChallengeLists" preserve="all" />
    <type fullname="Megagon.SnowRiders.Input.Tutorial.ControlsDetectionVisualizer" preserve="all" />
    <type fullname="Megagon.SnowRiders.MenuScenes.CurrentMenuSceneInstructions" preserve="all" />
    <type fullname="Megagon.SnowRiders.MenuScenes.MenuBackgroundSetups" preserve="all" />
    <type fullname="Megagon.SnowRiders.Networking.NetworkSessionData" preserve="all" />
    <type fullname="Megagon.SnowRiders.Physics.Helper.OverrideCenterOfMass" preserve="all" />
    <type fullname="Megagon.SnowRiders.Physics.Helper.OverrideInertia" preserve="all" />
    <type fullname="Megagon.SnowRiders.Physics.PlayerColliderModifier" preserve="all" />
    <type fullname="Megagon.SnowRiders.RuntimeLists.Snowriders.RuntimeListPlayer" preserve="all" />
    <type fullname="Megagon.SnowRiders.Score.PartyScore.Localization.TrickNameLocalization" preserve="all" />
    <type fullname="Megagon.SnowRiders.ScriptableObjects.ContextualVariableManager" preserve="all" />
    <type fullname="Megagon.SnowRiders.ScriptableObjects.FilteredAndChosenMatchFile" preserve="all" />
    <type fullname="Megagon.SnowRiders.ScriptableObjects.FilteredAndChosenReplayFile" preserve="all" />
    <type fullname="Megagon.SnowRiders.ScriptableObjects.LoadedScreenList" preserve="all" />
    <type fullname="Megagon.SnowRiders.ScriptableObjects.ReplayContext" preserve="all" />
    <type fullname="Megagon.SnowRiders.ScriptableObjects.ReplayDataStaging" preserve="all" />
    <type fullname="Megagon.SnowRiders.Snow.ImpactSnowParticleEmitter" preserve="all" />
    <type fullname="Megagon.SnowRiders.Snow.SnowParticleEmitter" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Activation.UI_Behavior_PlayAnimation_ThenDisable" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Activation.UI_Behavior_SetActivationBasedOnCreditsLevel" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Activation.UI_Behavior_SetActivationBasedOnCreditsOrTrainingLevel" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Activation.UI_Behavior_SetActivationBasedOnGhostAvailibility" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Activation.UI_Behavior_SetActivationBasedOnPlayerCrashed" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Activation.UI_Behavior_SetActivationBasedOnRespawnPointSet" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Audio.UI_Behavior_EmitFinishLineAudio" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Audio.UI_Behavior_EmitRaceStartAudio" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Customizations.UI_Behavior_ApplyPaintjobToAllGear" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Customizations.UI_Behavior_ModifyBody" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Customizations.UI_Behavior_ModifyVoice" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Customizations.UI_Behavior_OnPressedRandomization" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Customizations.UI_Behavior_PointToLocation" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Customizations.UI_Behavior_RandomizeCustomization" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Customizations.UI_Behavior_SetItemCategory" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Demo.UI_Behavior_CheckIfNextFest" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Demo.UI_Behavior_Gamescom_EnterEmail" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Demo.UI_Behavior_Gamescom_EnterName" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Demo.UI_Behavior_Gamescom_LockEnterMailButton" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Demo.UI_Behavior_Gamescom_LockEnterNameButton" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Demo.UI_Behavior_GamescomStartScreen" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Demo.UI_Behavior_NextFest_Wishlist" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Demo.UI_Behavior_NextFestCountdown" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Demo.UI_Behavior_PopupInputTypeManager" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Endscreens.UI_Behavior_RanksAndTimesToggle" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Gear.UI_Behavior_StartOrNextScreen" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.HUD.UI_Behavior_TriggerAnyButtonWhileInGame" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.HUD.UI_Behavior_TriggerContinueAfterConstrain" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.HUD.UI_Behavior_TriggerEmotesForUIInputs" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.HUD.UI_Behavior_TriggerPauseWhileInGame" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Leaderboards.UI_Behavior_LockButtonWhenNoLeaderboardRank" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Leaderboards.UI_Behavior_LockIfFirstLeaderboardPage" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Lobby.UI_Behavior_HideButtonIfMouseSelection" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Lobby.UI_Behavior_LockButtonIfMoreThanOnePlayer" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Lobby.UI_Behavior_ReadyUpOnEnable" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Lobby.UI_Behavior_StartTourButtonLockLogic" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Loca.UI_Behavior_JoystickLocaVariants" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Lock.UI_Behavior_Lock_InputText" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Lock.UI_Behavior_LockIfDemo" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Lock.UI_Behavior_LockIfGhostNotAvailable" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Lock.UI_Behavior_LockIfLocalPlayerIsSpectator" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Lock.UI_Behavior_LockIfLocalPlayerReady" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Lock.UI_Behavior_LockIfNextFestDemo" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Lock.UI_Behavior_LockIfOffline" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Lock.UI_Behavior_LockIfVersionOutdated" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Lock.UI_Behavior_LockRacingMode" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Lock.UI_Behavior_SetReadyIfTourCountdownCloseToEnd" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Navigation.UI_Behavior_ClearNavigationOverride" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Navigation.UI_Behavior_ResetDefaultSelection" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Navigation.UI_Behavior_UpdateContextButtons" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Popups.UI_Behavior_ClosePopup_OnlineState" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Replay.UI_Behavior_JumpToFrame" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Rewards.UI_Behavior_ShowLevelUpScreen" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Rewards.UI_Behavior_ShowNextReward" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Settings.UI_Behavior_CheckForUnappliedSettings" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Social.UI_Behavior_FriendFocusShift" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Social.UI_Behavior_Social_CheckForPermission" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Social.UI_Behavior_Social_ToggleCrossplatform" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Squads.UI_Behavior_LeaveLobby" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Squads.UI_Behavior_Lobbies_CheckForPendingRequests" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Squads.UI_Behavior_Lobbies_CheckIfMorePendingRequests" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Squads.UI_Behavior_Squads_AcceptExitSquads" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Squads.UI_Behavior_Squads_AreYouSureCheckInSquads" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Squads.UI_Behavior_Squads_BlockButtonSquadLogic" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Squads.UI_Behavior_Squads_CheckForMorePendingInvites" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Squads.UI_Behavior_Squads_CheckForPendingInvites" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Squads.UI_Behavior_Squads_EnableObjectsInSquadOrLobby" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Squads.UI_Behavior_Squads_LeaveSquad" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Squads.UI_Behavior_Squads_RefreshNetworkPlayerList" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Startup.UI_Behavior_InviteToDiscord" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Startup.UI_Behavior_SplashScreen" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Startup.UI_Behavior_StartupAnimation_Rotate" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Startup.UI_Behavior_StartupAnimation_Scale" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.ToasterMessages.UI_SendMessage_BackToLobby" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.ToasterMessages.UI_SendMessage_FailedJoining" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.ToasterMessages.UI_SendMessage_HostMigration" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.ToasterMessages.UI_SendMessage_KickedFromLobby" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.ToasterMessages.UI_SendMessage_LoadedAndJoining" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.ToasterMessages.UI_SendMessage_LobbyOwnerMigration" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.ToasterMessages.UI_SendMessage_OnlineStatus" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.ToasterMessages.UI_SendMessage_PlayerJoinedLobby" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.ToasterMessages.UI_SendMessage_PlayerJoinedSquad" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Trail.UI_Behavior_ChangeDifficulty" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Trail.UI_Behavior_LockIfHardModeNotAvailable" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Trail.UI_Behavior_LockOnNoTrailOrTrailsFull" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Trail.UI_Behavior_LockOnTrailsNotFull" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Trail.UI_Behavior_ResetCurrentDifficulty" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Trail.UI_Behavior_SaveLevelInfoToVariable" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Trail.UI_Behavior_SetRandomTrailsInTour" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Trail.UI_Behavior_SetToLastSelectedTrail" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Triggers.UI_ActionEventTrigger" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Triggers.UI_Behavior_VerifyUserSignedIn" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Triggers.UI_EventTriggerInvoker" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Triggers.UI_HideUITrigger" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Triggers.UI_KeyboardEventTrigger" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Triggers.UI_KeyboardEventTrigger_Gamescom" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Triggers.UI_NetworkEventTriggerInvoker" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Triggers.UI_TriggerOnEnable" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Tutorial.UI_Behavior_ClearLastTrailInfo" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Tutorial.UI_Behavior_GoToMainMenu" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Tutorial.UI_Behavior_LockIfPlayerHasntPlayedFirstTrail" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Tutorial.UI_Behavior_ResetGoToMainMenu" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Tutorial.UI_Behavior_SelectSteeringStyle" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Tutorial.UI_Behavior_SetActivationBasedOnFinishedTutorial" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Tutorial.UI_Behavior_SetActivationBasedOnGamepadType" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Tutorial.UI_Behavior_SetActivationBasedOnSettingsExist" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Tutorial.UI_Behavior_SetTrackingData" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Tutorial.UI_TriggerTeamModePopup" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.UI_Behavior_ClearTrailList" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.UI_Behavior_ForceLayoutUpdate" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.UI_Behavior_HoldButtonToClosePopUp" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.UI_Behavior_LockButtonBasedOnEvents" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.UI_Behavior_PlayPauseReplayButton" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.UI_Behavior_PopUpTimer" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.UI_Behavior_ReplaySpeed" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.UI_Behavior_ReverseFillArea" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.UI_Behavior_Scaler" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.UI_Behavior_SelectButton" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.UI_Behavior_SelectGameMode" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.UI_Behavior_SetActivationBasedOnAvailableChallenges" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.UI_Behavior_SetActivationBasedOnBestTimeActive" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.UI_Behavior_SetActivationBasedOnCallback" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.UI_Behavior_SetActivationBasedOnConfig" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.UI_Behavior_SetActivationBasedOnCurrentPlayerAmount" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.UI_Behavior_SetActivationBasedOnDemo" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.UI_Behavior_SetActivationBasedOnEndTimer" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.UI_Behavior_SetActivationBasedOnEvents" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.UI_Behavior_SetActivationBasedOnGameMode" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.UI_Behavior_SetActivationBasedOnGearSelected" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.UI_Behavior_SetActivationBasedOnIsTrainingLevel" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.UI_Behavior_SetActivationBasedOnLobbyType" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.UI_Behavior_SetActivationBasedOnNextTrail" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.UI_Behavior_SetActivationBasedOnOnlineState" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.UI_Behavior_SetActivationBasedOnPlayerFinished" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.UI_Behavior_SetActivationBasedOnReadyState" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.UI_Behavior_SetActivationBasedOnReleaseMode" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.UI_Behavior_SetActivationBasedOnSessionType" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.UI_Behavior_SetActivationBasedOnSquadState" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.UI_Behavior_SetActivationBasedOnTourStatus" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.UI_Behavior_SetActivationBasedOnVariable" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.UI_Behavior_SetActivationBasedOnVersionState" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.UI_Behavior_SetActivationBasedOnXBox" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.UI_Behavior_ShowWhenPlayersAreNotReady" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.UI_Behavior_TriggerNetworkEventOnEnable" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.UI_Behavior_UnifyFontSizes" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.UI_ToasterMessage" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.ColorProvider.ColorScheme" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.ColorProvider.UI_ColorImageSelector" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.ColorProvider.UI_ColorSchemeSelectionEntry" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.ColorProvider.UI_ColorSpriteRendererSelector" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.ColorProvider.UI_ColorTextSelector" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Controls.UI_ApplicationQuitButton" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Controls.UI_ControllerRumbleSelection" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Controls.UI_ControlModeSelection" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Controls.UI_FloatVariableSlider" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Controls.UI_IntVariableSlider" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Controls.UI_LanguageSelection" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Controls.UI_MasterVolumeSelection" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Controls.UI_TrailSelection" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.CustomNavigation.Elements.UI_Button" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.CustomNavigation.Elements.UI_Dropdown" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.CustomNavigation.Elements.UI_DropdownEntry" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.CustomNavigation.Elements.UI_Field" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.CustomNavigation.Elements.UI_FloatSlider" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.CustomNavigation.Elements.UI_InputField" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.CustomNavigation.Elements.UI_IntSlider" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.CustomNavigation.Elements.UI_MouseOnlyButton" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.CustomNavigation.Elements.UI_Toggle" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.CustomNavigation.UI_CustomEventTrigger" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.CustomNavigation.UI_ProgressBar" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.CustomNavigation.UI_ProgressCircle" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.CustomNavigation.UI_ScreenNavigationInfo" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.CustomUIElements.UI_CustomContentSizeFitter" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.CustomUIElements.UI_CustomScrollRect" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.CustomUIElements.UI_CustomSpriteScaler" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.CustomUIElements.UI_EnumerableGearHeader" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.CustomUIElements.UI_EnumerableHeader" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.CustomUIElements.UI_EnumerableMountainHeader" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.CustomUIElements.UI_NavigationBar" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.CustomUIElements.UI_NavigationBar_Customization" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.CustomUIElements.UI_NavigationBarButton" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.CustomUIElements.UI_NavigationBarButton_Customization" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Challenges.ChallengeDisplayValues" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Challenges.UI_ChallengeHUDEntry" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Challenges.UI_ChallengeMenuEntry" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Challenges.UI_DisplayBestRank_NextTrail" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Challenges.UI_DisplayBestTime_NextTrail" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Challenges.UI_DisplayLocalPlayerFinalSessionTime" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Challenges.UI_PersistentChallengeDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Challenges.UI_TutorialChallengeHUDEntry" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Challenges.UI_TutorialChallengeListDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Checkpoints.UI_BestTimeCheckpointDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Checkpoints.UI_CheckpointEntry" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Checkpoints.UI_CheckpointInfos" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Checkpoints.UI_MissedCheckpointDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Checkpoints.UI_RankCheckpointDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Checkpoints.UI_SectionTimeCheckpointDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Checkpoints.UI_SessionTimeCheckpointDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.ContextualElements.UI_ReplayDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.ContextualElements.UI_ReplayFrameDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.ContextualElements.UI_ReplayInputDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.ContextualElements.UI_RespawnPointEntry" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.ContextualElements.UI_RespawnPointsDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Customizations.UI_HairColor" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Customizations.UI_HairCustomizationScheme" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Customizations.UI_ItemColor" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Customizations.UI_ItemColorPreview" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Customizations.UI_ItemColorPreviewEntry" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Customizations.UI_ItemCustomizationScheme" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Customizations.UI_PurchaseItemDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Customizations.UI_ShopItem" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Customizations.UI_ShopItemList" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Customizations.UI_ShopPopUpBehaviour" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Customizations.UI_SkinCustomizationScheme" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Customizations.UI_SkinTone" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Customizations.UI_VoiceEntry" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Debug.UI_FPSDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Debug.UI_IsHostDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Debug.UI_IsLobbyOwnerDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Debug.UI_PingDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Debug.UI_SPSDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Demo.UI_DemoEndCountdownDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.DisplayPlayerList.UI_DisplayPlayerDataListEntry" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.DisplayPlayerList.UI_DisplayPlayerList" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Emotes.UI_Emote" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Emotes.UI_EmotePreview" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Emotes.UI_EmotePreview_CooldownLogic" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Emotes.UI_EmotePreview_InactionLogic" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Emotes.UI_EmotePreviewEntry" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Endscreens.Badges.UI_Badge" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Endscreens.Badges.UI_Badges" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Endscreens.UI_CurrentTrail_MountainDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Endscreens.UI_CurrentTrail_TrailDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Endscreens.UI_CurrentTrail_TrailTourCount" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Endscreens.UI_TourDifficultyDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Fake.UI_Fake_CrashCount" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Fake.UI_Fake_LocalScore" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Fake.UI_Fake_RacingRanking" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Fake.UI_Fake_SectionDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Fake.UI_Fake_SessionTime" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Fake.UI_Fake_TeamScoreDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.GameData.UI_GameVersionText" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.GameData.UI_VersionDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Gear.GearDisplayValues" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Gear.UI_CurrentGearStats" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Gear.UI_GearSelection" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Gear.UI_GearShopButton" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Gear.UI_GearStat" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Ghosts.UI_GhostButtonCheckmark" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Ghosts.UI_GhostButtonHandler" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.HUD.ControlDetection.UI_ControlDetectionDisplays" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.HUD.ControlDetection.UI_ControlDetectionHandler" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.HUD.ControlDetection.UI_ControllerDetectionDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.HUD.ControlDetection.UI_ControlSchemeTextDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.HUD.HUD_RestartUI" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.HUD.UI_BestSectionTimeDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.HUD.UI_CrashDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.HUD.UI_Debug_TrailInfo" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.HUD.UI_EndTimer" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.HUD.UI_LocalSavedScoreDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.HUD.UI_LocalUnsavedScoreDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.HUD.UI_Rank" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.HUD.UI_SectionDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.HUD.UI_SectionTimer" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.HUD.UI_SessionTimer" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.HUD.UI_SpectatorSelection" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Leaderboards.UI_FilterSelectable" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Leaderboards.UI_Leaderboard" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Leaderboards.UI_LeaderboardDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Leaderboards.UI_LeaderboardEntry" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Leaderboards.UI_LeaderboardFilter" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Leaderboards.UI_LeaderboardGearFilter" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Leaderboards.UI_LeaderboardMountainFilter" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.LevelTexts.CreditEntries" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.LevelTexts.InWorldTextEntries" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.LevelTexts.UI_InWorldTextDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.LevelTexts.UI_InWorldTextEntry" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Loading.UI_LoadingDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Loading.UI_LoadingImage" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Loading.UI_LoadingTipsDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Lobby.UI_LobbyAmountDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Lobby.UI_PlatformLockedButtons" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Lobby.UI_PrivateLobbyContext" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Lobby.UI_PrivateLobbyIDDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Lobby.UI_QuickmatchLobbyDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Network.UI_OfflineDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.PlayerContext.UI_BlockedEntry" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.PlayerContext.UI_BlockedList" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.PlayerNameLabels.UI_LobbyPlayerNameLabels" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.PlayerNameLabels.UI_PlayerEmoteStateHandler" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.PlayerNameLabels.UI_PlayerIsLobbyOwner" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.PlayerNameLabels.UI_PlayerIsLocalPlayer" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.PlayerNameLabels.UI_PlayerIsReady" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.PlayerNameLabels.UI_PlayerNameLabels" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.PlayerNameLabels.UI_PlayerNameLabelsEntry" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.PlayerNameLabels.UI_TeamChainPlayerDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Profile.UI_LocalPlayerAvatarDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Profile.UI_NameDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Profile.UI_PlayerActionConfirmationDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Profile.UI_PlayerInfoPopupDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Profile.UI_PlayerNameSelectionDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Rewards.UI_CareerRewardBox" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Rewards.UI_CurrencyDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Rewards.UI_ExperienceDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Rewards.UI_ItemDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Rewards.UI_LevelDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Rewards.UI_LevelUpDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Rewards.UI_LevelUpEntry" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Rewards.UI_RewardBox" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Rewards.UI_RewardEntry" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.SectionRankingDisplay.UI_SectionRankingDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.SectionRankingDisplay.UI_SectionRankingEntry" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.SectionRankingDisplay.UI_TimesAndRanksResultDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.SectionRankingDisplay.UI_TimesResultEntry" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.SectionTimesDisplay.UI_SectionTimeOverviewDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.SectionTimesDisplay.UI_SectionTimeOverviewDisplayEntry" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Social.UI_ParentalControlButton" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Squads.UI_LobbyJoinRequestDisplayHandler" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Squads.UI_NetworkPlayerListDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Squads.UI_NetworkPlayerListEntry" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Squads.UI_SquadInviteDisplayHandler" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Team.UI_PartyModeFinalScoreMultiplierDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Team.UI_PartyModeGoalsDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Team.UI_PartyModeGoalsDisplayEntry" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Team.UI_PartyModeOwnScoreDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Team.UI_PartyModeOwnScoreDisplayEntry" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Team.UI_PartyModeScoreMultiplierDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Team.UI_PartyModeScoreMultiplierDisplayEntry" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Team.UI_PartyModeTeamScoreDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Team.UI_PartyModeTeamScoreDisplayEntry" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Team.UI_PartyModeTeamScoreResultDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Team.UI_RespawnPointDisplays" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Team.UI_RespawnPointScoreEntry" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Trails.DifficultyDisplayDataProvider" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Trails.UI_ChallengePreview" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Trails.UI_ChallengeProgressionSlider" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Trails.UI_ChallengesPreview" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Trails.UI_MountainButtonDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Trails.UI_NextTrail_IconDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Trails.UI_NextTrail_MountainDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Trails.UI_NextTrail_Thumbnail" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Trails.UI_NextTrail_TrailDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Trails.UI_NextTrailCountDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Trails.UI_RestingSpotPreview" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Trails.UI_SelectedTrailDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Trails.UI_SelectedTrailsDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Trails.UI_Tour_NextTrailCountdown" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Trails.UI_TrailMap" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Trails.UI_TrailMapDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Tutorial.UI_TutorialChallengeFailedPopup" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Tutorial.UI_TutorialCheckpointEntry" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Tutorial.UI_TutorialCheckpointInfos" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.UI_CurrentRankDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.UI_CurrentTrailThumbnailDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.UI_Debug_BoolSettingDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.UI_Debug_GameSequenceVisualization" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.UI_Debug_PlayerNameDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.UI_Debug_ScreenResolution" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.UI_Debug_SelectionState" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.UI_DebugHostMigrationOptions" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.UI_DebugNetworkMonitorDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.UI_FilterableReplayList" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.UI_FinishStatsDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.UI_GameFeedSourceReader" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.UI_GameSessionInfoList" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.UI_GameSessionSetup" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.UI_GameStateDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.UI_MatchList" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.UI_MatchListEntry" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.UI_NetworkLobbyEntry" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.UI_NetworkLobbyFilter" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.UI_NetworkLobbyList" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.UI_NetworkSessionSetup_Host" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.UI_NetworkSessionSetup_Join" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.UI_NetworkStateDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.UI_PlayerTimeRankingHeader" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.UI_PressToStartDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.UI_RankDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.UI_ReplayListEntry" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.UI_SceneTransition" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.UI_ScoreThresholdDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.UI_ScreenProgressionDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.UI_ScreenProgressionEntry" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.UI_StartCountdownDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.UI_TimerReader" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.UI_ToasterMessengerDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.UI_TourLocalResultDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.UI_TourTrailSelectionDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.UI_TourTrailSelectionDisplayEntry" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Displays.UI_TrailInfo" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Effects.AudioEffects.UI_AudioData" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Effects.AudioEffects.UI_AudioEffectPlayer" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Effects.UI_AnimationData" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Effects.UI_AnimationEffectPlayer" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Effects.UI_EffectGroup" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Effects.UI_EffectSequence" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Effects.UI_EffectTrigger" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.FontSizeProvider.FontScheme" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.FontSizeProvider.FontSizeSelector" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.HUD.UI_Behavior_SetActivationBasedOnRestingSpotTriggered" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.HUD.UI_ClimbingGearDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.HUD.UI_CustomRespawnPointLocalDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.HUD.UI_CustomRespawnPointRemoteDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.HUD.UI_CustomRespawnPointRemoteEntry" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.HUD.UI_RecoverDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.HUD.UI_RestingSpotDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.HUD.UI_RestingSpotScreen" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Other.Console.ConsoleDisplayValues" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Other.Loading.UILoadingDisplayData" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Rewired.UI_DynamicIcon" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Rewired.UI_DynamicIcons" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Rewired.UI_RecoverDisplayTextLabel" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.RuntimeLists.RuntimeListColorScheme" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Screens.DebugMenu.UI_Debug_AudioDebugInfo" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Screens.DebugMenu.UI_Debug_LastScreenHandler" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Screens.DebugMenu.UI_Debug_NetworkGameloopDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Screens.DebugMenu.UI_Debug_NetworkInputDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Screens.DebugMenu.UI_Debug_NetworkTransportDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Screens.DebugMenu.UI_Debug_PlayerDebugInfo" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Screens.DebugMenu.UI_Debug_ResetTutorialTexts" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Screens.DebugMenu.UI_DebugMenu" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Screens.DebugMenu.UI_DebugMenuScreen" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Screens.UI_LoadedScreensHandler" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Screens.UI_MainScreen" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Screens.UI_PopUp" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Screens.UI_PopUpParent" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Screens.UI_ScreenParent" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Screens.UI_SubScreen" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.ScriptableObjects.AdditionalLevelRewards" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.ScriptableObjects.FontSchemeVariable" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.ScriptableObjects.LoadingScreenTips.ControllerTips" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.ScriptableObjects.LoadingScreenTips.GeneralTips" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.ScriptableObjects.LoadingScreenTips.KeyboardMouseTips" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.ScriptableObjects.LoadingScreenTips.LoadingScreenTips" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.ScriptableObjects.LoadingScreenTips.MultiplayerTips" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.ScriptableObjects.LoadingScreenTips.RacingTips" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.ScriptableObjects.LoadingScreenTips.SingleplayerTips" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.ScriptableObjects.LoadingScreenTips.SoloTips" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.ScriptableObjects.LoadingScreenTips.TeamModeTips" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.ScriptableObjects.LoadingScreenTips.ZenTips" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.ScriptableObjects.LocalizedRanks" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Settings.SettingsTypePrefabMap" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Settings.UI_EnumSetting" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Settings.UI_FloatSliderSetting" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Settings.UI_InputFieldSetting" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Settings.UI_IntSliderSetting" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Settings.UI_ItemSetting" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Settings.UI_SettingCategoryScreen" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Settings.UI_SettingHandler" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Settings.UI_SettingsButton" preserve="all" />
    <type fullname="Megagon.SnowRiders.UI.Settings.UI_ToggleSetting" preserve="all" />
    <type fullname="Megagon.SnowRiders.Variables.Snowriders.CrashCountRuntimeList" preserve="all" />
    <type fullname="Megagon.SnowRiders.Variables.Snowriders.LocalPlayerCurrentCrashesVariable" preserve="all" />
    <type fullname="Megagon.SnowRiders.Variables.Snowriders.LocalPlayerCurrentRankVariable" preserve="all" />
    <type fullname="Megagon.SnowRiders.Variables.Snowriders.LocalPlayerCurrentSectionVariable" preserve="all" />
    <type fullname="Megagon.SnowRiders.Variables.Snowriders.LocalPlayerTotalTimeVariable" preserve="all" />
    <type fullname="Megagon.SnowRiders.Variables.Snowriders.PlayerRankingRuntimelist" preserve="all" />
    <type fullname="Megagon.SnowRiders.Vehicle.Behaviour.SkiBehaviourConfig" preserve="all" />
    <type fullname="Megagon.SnowRiders.Vehicle.Behaviour.SkiVehicleBehaviour" preserve="all" />
    <type fullname="Megagon.SnowRiders.Vehicle.Behaviour.SnowboardBehaviourConfig" preserve="all" />
    <type fullname="Megagon.SnowRiders.Vehicle.Behaviour.SnowboardVehicleBehaviour" preserve="all" />
    <type fullname="Megagon.SnowRiders.Vehicle.Behaviour.VehicleConfig" preserve="all" />
    <type fullname="Megagon.SnowRiders.Vehicle.Behaviour.VehicleCrashBehaviour" preserve="all" />
    <type fullname="Megagon.SnowRiders.Vehicle.Behaviour.VehicleCrashConfig" preserve="all" />
    <type fullname="Megagon.SnowRiders.Vehicle.Display.VehicleDisplay" preserve="all" />
    <type fullname="Megagon.SnowRiders.Vehicle.VehicleTypeDisplayData" preserve="all" />
    <type fullname="Megagon.Utility.Physics.BreakableJointParentConstraintLink" preserve="all" />
    <type fullname="Megagon.Utility.Variables.SyncableGameSessionLocalPlayerStatisticsVariable" preserve="all" />
    <type fullname="UI_Behavior_LockIfTourBackToLobby" preserve="all" />
    <type fullname="UI_BestTimeTextDisplay" preserve="all" />
    <type fullname="UI_ChainCountDisplay" preserve="all" />
    <type fullname="UI_Debug_PopUpTrigger" preserve="all" />
    <type fullname="UI_TutorialTeamModePopUp" preserve="all" />
    <type fullname="UI_TutorialTeamModePopUp_Entry" preserve="all" />
    <type fullname="Megagon.SnowRiders.Animation.Camera.RandomSinMovement/MotionLayer" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.Cameras.DynamicCameraCollisionParameters" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.Cameras.DynamicCameraParameters" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.MenuScenes.MenuBackgroundSetup" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.Achievements.AchievementGroup" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.Achievements.AchievementSet" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.Animation.IdleAnimationGroup" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.Animation.SittingStyleAnimations" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.Vehicle.Behaviour.SkiAnimationCurveGroup" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.Vehicle.Behaviour.SkiAnimationVelocityCurveGroup" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.Vehicle.NetworkInterpolation.VehicleInterpolationConfiguration" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.ButtonBehavior" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.UI.Behaviors.Startup.SplashScreenSegment" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.UI.ColorProvider.UI_OverrideEffectHandler" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.UI.ColorProvider.UI_OverrideEffectHandler/OverridableGroup" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.UI.CustomNavigation.DirectionTypeAndSelectable" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.UI.CustomNavigation.SelectableEffect" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.UI.CustomNavigation.UI_EffectPresets" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.UI.CustomNavigation.UI_SelectableClickHandler" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.UI.CustomNavigation.UI_SelectableNeighborCollection" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.UI.CustomNavigation.UI_SelectableNeighbors" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.UI.CustomNavigation.UI_SelectableSelectHandler" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.UI.CustomUIElements.ButtonExplanationSettings" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.UI.CustomUIElements.EnumerableEntries" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.UI.CustomUIElements.ExplanationTypeSettings" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.UI.CustomUIElements.LockSettings" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.UI.CustomUIElements.UI_InputIconHandlerData" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Challenges.ChallengeTypeDisplayValues" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.UI.Displays.CountdownSegment" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Endscreens.Badges.BadgeDisplayData" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.UI.Displays.FinishStatsPerGameMode" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.UI.Displays.LevelTexts.CreditEntry" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.UI.Displays.LevelTexts.InWorldTextEntry" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.UI.Displays.LevelTexts.InputOption" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.UI.Displays.LevelTexts.InputTypeLocaPair" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Network.NetworkStateDisplayValues" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Profile.PlayerActionConfirmationData" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Team.UI_PartyModeOwnScoreDisplay/ScoreType" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Team.UI_PartyModeScoreMultiplierDisplay/Multiplier" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Team.UI_PartyModeScoreMultiplierDisplay/ScoreThreshold" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Trails.DifficultySettings" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.UI.FontSizeProvider.FontScheme/FontSchemeEntry" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.UI.Other.Audio.UIAudioSettings" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.UI.Other.Console.PlatformDisplayData" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.UI.Other.Loading.UILoadingData" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.UI.ScriptableObjects.LevelReward" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.UI.ScriptableObjects.LoadingScreenTips.LoadingScreenEntry" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.Gameplay.Tutorial.TutorialChallenge" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.Configuration.SubConfigurations.Helper.ShaderKeywordSetting" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.Configuration.SubConfigurations.MfuscatorOptions" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.Gameplay.RestingSpots.CameraPoint" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.UI.Displays.Gear.StatDescription" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.UI.Settings.IntSettingsTypePrefabPair" preserve="nothing" serialized="true" />
    <type fullname="Megagon.SnowRiders.UI.Settings.SettingsTypePrefabPair" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Megagon.SnowRiders.Customization, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Assets.Assemblies.Megagon.SnowRiders.Customization.CustomizationOverride" preserve="all" />
    <type fullname="Megagon.SnowRiders.Customization.CustomizationDB" preserve="all" />
    <type fullname="Megagon.SnowRiders.Customization.Items.CustomizationItem" preserve="all" />
    <type fullname="Megagon.SnowRiders.Customization.Items.CustomizationItemCollection" preserve="all" />
    <type fullname="Megagon.SnowRiders.Customization.Tricks.Trick" preserve="all" />
    <type fullname="Megagon.SnowRiders.Customization.Tricks.TrickCollection" preserve="all" />
    <type fullname="Megagon.SnowRiders.Customization.Vehicles.VehicleCustomization" preserve="all" />
    <type fullname="Megagon.SnowRiders.Customization.Vehicles.VehicleCustomizationCollection" preserve="all" />
  </assembly>
  <assembly fullname="Megagon.SnowRiders.EntitledContent, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Megagon.SnowRiders.EntitledContent.DemoLevelEvaluator" preserve="all" />
    <type fullname="Megagon.SnowRiders.EntitledContent.EntitledContentCollection" preserve="all" />
    <type fullname="Megagon.SnowRiders.EntitledContent.EntitledContentDescriptor" preserve="all" />
    <type fullname="Megagon.SnowRiders.EntitledContent.LicenseContentEvaluator" preserve="all" />
  </assembly>
  <assembly fullname="Megagon.Social.Playfab.Transport, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Megagon.Social.PlayFab.MirrorTransport.PlayFabPartyTransport" preserve="all" />
  </assembly>
  <assembly fullname="Megagon.Utility, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Megagon.SnowRiders.ScriptableObjects.LobbyStateVariable" preserve="all" />
    <type fullname="Megagon.Utility.Animation.ConfigurableJointTargetMatcher" preserve="all" />
    <type fullname="Megagon.Utility.Animation.RenderRig" preserve="all" />
    <type fullname="Megagon.Utility.Animation.SimulationRig" preserve="all" />
    <type fullname="Megagon.Utility.Debugging.NetworkSessionDebugData" preserve="all" />
    <type fullname="Megagon.Utility.Effects.OrthographicDepthmap" preserve="all" />
    <type fullname="Megagon.Utility.Effects.PostProcessing.ShadowMapStabiliser" preserve="all" />
    <type fullname="Megagon.Utility.Effects.ShadowMapExposer" preserve="all" />
    <type fullname="Megagon.Utility.Effects.TransparencyMap" preserve="all" />
    <type fullname="Megagon.Utility.Inputs.Hotkeys.HotkeyController" preserve="all" />
    <type fullname="Megagon.Utility.Inputs.Hotkeys.HotkeyDatabaseConfiguration" preserve="all" />
    <type fullname="Megagon.Utility.IOGeneric.PersistentStorageConfiguration" preserve="all" />
    <type fullname="Megagon.Utility.Physics.LiquidCollisionTrigger" preserve="all" />
    <type fullname="Megagon.Utility.Physics.RigidbodyCollisionForwarder" preserve="all" />
    <type fullname="Megagon.Utility.Rendering.BlueNoiseProvider" preserve="all" />
    <type fullname="Megagon.Utility.Rendering.MeshPreprocessor.MeshOffsetGrids.MeshOffsetGrid" preserve="all" />
    <type fullname="Megagon.Utility.Rendering.RenderScalerInput" preserve="all" />
    <type fullname="Megagon.Utility.Rendering.RenderScalerOutput" preserve="all" />
    <type fullname="Megagon.Utility.Rendering.Screenshot" preserve="all" />
    <type fullname="Megagon.Utility.Rendering.WorldSpaceUITransfer" preserve="all" />
    <type fullname="Megagon.Utility.RuntimeLists.RuntimeListAnimationClip" preserve="all" />
    <type fullname="Megagon.Utility.RuntimeLists.RuntimeListString" preserve="all" />
    <type fullname="Megagon.Utility.ScriptableObjects.RewiredIconProvider" preserve="all" />
    <type fullname="Megagon.Utility.SettingHandlers.GraphicsSettingHandlerConfig" preserve="all" />
    <type fullname="Megagon.Utility.SettingHandlers.PostProcessingSettingHandler" preserve="all" />
    <type fullname="Megagon.Utility.Settings.BoolSetting" preserve="all" />
    <type fullname="Megagon.Utility.Settings.FloatSetting" preserve="all" />
    <type fullname="Megagon.Utility.Settings.IntSetting" preserve="all" />
    <type fullname="Megagon.Utility.Settings.SettingCategory" preserve="all" />
    <type fullname="Megagon.Utility.Settings.SettingsPresets" preserve="all" />
    <type fullname="Megagon.Utility.Settings.StringSetting" preserve="all" />
    <type fullname="Megagon.Utility.Shaders.TerrainBlending.TerrainBlendingCamera" preserve="all" />
    <type fullname="Megagon.Utility.StateMachine.WaitForActionEvent" preserve="all" />
    <type fullname="Megagon.Utility.StateMachine.WaitForNetworkEvent" preserve="all" />
    <type fullname="Megagon.Utility.StateMachine.WaitForSystemEvent" preserve="all" />
    <type fullname="Megagon.Utility.Variables.BoolVariable" preserve="all" />
    <type fullname="Megagon.Utility.Variables.ContainerVariable" preserve="all" />
    <type fullname="Megagon.Utility.Variables.ContextualIntVariable" preserve="all" />
    <type fullname="Megagon.Utility.Variables.ContextualUIntVariable" preserve="all" />
    <type fullname="Megagon.Utility.Variables.ContextualVariables.ContextualRuntimeListInt" preserve="all" />
    <type fullname="Megagon.Utility.Variables.ContextualVariables.ContextualRuntimeListIntVariable" preserve="all" />
    <type fullname="Megagon.Utility.Variables.FloatVariable" preserve="all" />
    <type fullname="Megagon.Utility.Variables.GameModeConfigurationVariable" preserve="all" />
    <type fullname="Megagon.Utility.Variables.IntVariable" preserve="all" />
    <type fullname="Megagon.Utility.Variables.StringVariable" preserve="all" />
    <type fullname="Megagon.Utility.Variables.TransformVariable" preserve="all" />
    <type fullname="Megagon.Utility.Variables.UIntVariable" preserve="all" />
    <type fullname="Megagon.Utility.Variables.Vector3Variable" preserve="all" />
    <type fullname="Megagon.Utility.IOGeneric.PersistentStorageConfiguration/FileName" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Utility.IOGeneric.PersistentStorageConfiguration/PersistantModuleFileName" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Utility.IOGeneric.PersistentStorageConfiguration/PersistantModuleFolder" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Utility.ResourceManagement.ManagedAssetReference" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Utility.Settings.SettingPreset" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Utility.ScriptableObjects.ControllerEntry" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Utility.ScriptableObjects.IconEntry" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Utility.ScriptableObjects.KeyboardEntry" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Utility.ScriptableObjects.KeyboardIconEntry" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Utility.Effects.DaytimeQuaternion" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Utility.Inputs.Hotkeys.Hotkey" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Utility.Inputs.Hotkeys.HotkeyCombination" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Utility.Inputs.Hotkeys.HotkeyDatabase" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Utility.Inputs.Hotkeys.HotkeyGroup" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Utility.ResourceManagement.EditorAssetReference" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Utility.ResourceManagement.ManagedTexture" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Utility.ResourceManagement.ManagedTextureEntry" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Megagon.Utility.FMOD, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Megagon.Utility.FMOD.AudioEventEmitter" preserve="all" />
    <type fullname="Megagon.Utility.FMOD.CustomEventEmitter" preserve="all" />
  </assembly>
  <assembly fullname="Megagon.Utility.Interface, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Megagon.Utility.Interface.Types.GameMode.GameModeConfiguration" preserve="all" />
    <type fullname="Megagon.Utility.Interface.Types.GameMode.PartyModeConfiguration" preserve="all" />
    <type fullname="Megagon.Utility.Interface.Types.GameMode.TrickConfiguration" preserve="all" />
    <type fullname="Megagon.Utility.Interface.Types.Cameras.CameraModifierParameters" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Utility.Interface.Types.Cameras.MenuCameraTarget" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Utility.Interface.Types.GameMode.CrashModeConfiguration" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Utility.Interface.Types.GameMode.EndConditionConfiguration" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Utility.Interface.Types.GameMode.FlipConfiguration" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Utility.Interface.Types.GameMode.FullfilmentType" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Utility.Interface.Types.GameMode.GeneralGameModeConfiguration" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Utility.Interface.Types.GameMode.GoalConfiguration" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Utility.Interface.Types.GameMode.GoalConfigurations" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Utility.Interface.Types.GameMode.GrabConfiguration" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Utility.Interface.Types.GameMode.GroupMultiplierConfiguration" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Utility.Interface.Types.GameMode.HighSpeedMultiplierConfiguration" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Utility.Interface.Types.GameMode.ImplicitModifierConfiguration" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Utility.Interface.Types.GameMode.ImplicitRestrictionConfiguration" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Utility.Interface.Types.GameMode.MultiplierConfiguration" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Utility.Interface.Types.GameMode.NoCrashPointBonusConfiguration" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Utility.Interface.Types.GameMode.PlayerFulfillmentType" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Utility.Interface.Types.GameMode.PointBonusConfiguration" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Utility.Interface.Types.GameMode.RespawnPointConfiguration" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Utility.Interface.Types.GameMode.RespawnPointLeftGroupBonus" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Utility.Interface.Types.GameMode.RestrictionTypeAndParameter" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Utility.Interface.Types.GameMode.Reward" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Utility.Interface.Types.GameMode.RewardConfigurations" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Utility.Interface.Types.GameMode.RideOnWaterMultiplierConfiguration" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Utility.Interface.Types.GameMode.RidingBackwardsMultiplierConfiguration" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Utility.Interface.Types.GameMode.ScoringModeConfiguration" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Utility.Interface.Types.GameMode.SpinConfiguration" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Utility.Interface.Types.GameMode.TimeThresholdPointBonusConfiguration" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Megagon.Utility.PropertyGrid, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Megagon.Utility.PropertyGrid.Fields.BoolField" preserve="all" />
    <type fullname="Megagon.Utility.PropertyGrid.Fields.ButtonField" preserve="all" />
    <type fullname="Megagon.Utility.PropertyGrid.Fields.CollectionField" preserve="all" />
    <type fullname="Megagon.Utility.PropertyGrid.Fields.DropdownSelectorField" preserve="all" />
    <type fullname="Megagon.Utility.PropertyGrid.Fields.EnumField" preserve="all" />
    <type fullname="Megagon.Utility.PropertyGrid.Fields.LinkGroupField" preserve="all" />
    <type fullname="Megagon.Utility.PropertyGrid.Fields.MinMaxField" preserve="all" />
    <type fullname="Megagon.Utility.PropertyGrid.Fields.MultiSelectionField" preserve="all" />
    <type fullname="Megagon.Utility.PropertyGrid.Fields.MultiSelectionGroup" preserve="all" />
    <type fullname="Megagon.Utility.PropertyGrid.Fields.MultiSelectionItem" preserve="all" />
    <type fullname="Megagon.Utility.PropertyGrid.Fields.NumericField" preserve="all" />
    <type fullname="Megagon.Utility.PropertyGrid.Fields.SelectorField" preserve="all" />
    <type fullname="Megagon.Utility.PropertyGrid.Fields.StringField" preserve="all" />
    <type fullname="Megagon.Utility.PropertyGrid.Fields.TabCollectionField" preserve="all" />
    <type fullname="Megagon.Utility.PropertyGrid.Fields.TargetCollectionField" preserve="all" />
    <type fullname="Megagon.Utility.PropertyGrid.Fields.TargetField" preserve="all" />
    <type fullname="Megagon.Utility.PropertyGrid.Fields.VectorField" preserve="all" />
    <type fullname="Megagon.Utility.PropertyGrid.UI.FoldoutButton" preserve="all" />
    <type fullname="Megagon.Utility.PropertyGrid.UI.SelectionToggleButton" preserve="all" />
  </assembly>
  <assembly fullname="Megagon.Utility.UI, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Megagon.Utility.UI.ContextMenu.ContextMenuController" preserve="all" />
    <type fullname="Megagon.Utility.UI.ContextMenu.MenuGroupUI" preserve="all" />
    <type fullname="Megagon.Utility.UI.EventTarget" preserve="all" />
    <type fullname="Megagon.Utility.UI.IconGenerator" preserve="all" />
    <type fullname="Megagon.Utility.UI.ImageButton" preserve="all" />
    <type fullname="Megagon.Utility.UI.MinMaxSlider" preserve="all" />
    <type fullname="Megagon.Utility.UI.MultiColorImage" preserve="all" />
    <type fullname="Megagon.Utility.UI.SpriteToggle" preserve="all" />
    <type fullname="Megagon.Utility.UI.TextToggle" preserve="all" />
    <type fullname="Megagon.Utility.UI.TextureToggle" preserve="all" />
    <type fullname="Megagon.Utility.UI.TMPBackground" preserve="all" />
    <type fullname="Megagon.Utility.UI.Tooltip.HotkeyTooltipListener" preserve="all" />
    <type fullname="Megagon.Utility.UI.Tooltip.TooltipController" preserve="all" />
    <type fullname="Megagon.Utility.UI.Tooltip.TooltipProvider" preserve="all" />
    <type fullname="Megagon.Utility.UI.Tooltip.TooltipUI" preserve="all" />
    <type fullname="Megagon.Utility.UI.MinMaxSlider/SliderEvent" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Mirror, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="IgnoranceTransport.Ignorance" preserve="all" />
    <type fullname="Mirror.NetworkIdentity" preserve="all" />
    <type fullname="IgnoranceCore.IgnoranceChannelTypes" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="ParadoxNotion, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="NodeCanvas.Framework.Blackboard" preserve="all" />
    <type fullname="NodeCanvas.StateMachines.FSM" preserve="all" />
    <type fullname="NodeCanvas.StateMachines.FSMOwner" preserve="all" />
    <type fullname="NodeCanvas.Framework.Internal.GraphSource" preserve="nothing" serialized="true" />
    <type fullname="ParadoxNotion.Serialization.SerializationPair" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Platform.PC, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Platform.PC.PCQualityPresetsDefaultProcessor" preserve="all" />
  </assembly>
  <assembly fullname="Rewired, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Rewired.InputManager" preserve="all" />
  </assembly>
  <assembly fullname="Rewired_Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Rewired.Data.ControllerDataFiles" preserve="all" />
    <type fullname="Rewired.Data.Mapping.CustomCalculation_Accelerometer" preserve="all" />
    <type fullname="Rewired.Data.Mapping.CustomCalculation_CompareElementValues" preserve="all" />
    <type fullname="Rewired.Data.Mapping.CustomCalculation_FirstNonZero" preserve="all" />
    <type fullname="Rewired.Data.Mapping.CustomCalculation_LogitechGRacingWheelPedals" preserve="all" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap" preserve="all" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickTemplateMap" preserve="all" />
    <type fullname="Rewired.ActionElementMap" preserve="nothing" serialized="true" />
    <type fullname="Rewired.ControllerElementIdentifier" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.ConfigVars" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.ConfigVars/EditorVars" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.ConfigVars/PlatformVars" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.ConfigVars/PlatformVars_GameCoreScarlett" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.ConfigVars/PlatformVars_GameCoreXboxOne" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.ConfigVars/PlatformVars_LinuxStandalone" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.ConfigVars/PlatformVars_OSXStandalone" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.ConfigVars/PlatformVars_PS5" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.ConfigVars/PlatformVars_WindowsStandalone" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.ConfigVars/PlatformVars_WindowsUWP" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.ControllerMapEnabler_RuleSet_Editor" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.ControllerTemplateElementIdentifier_Editor" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.ActionCategoryMap" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.ActionCategoryMap/Entry" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.AxisCalibrationInfo" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.ControllerMap_Editor" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareAxisInfo" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareButtonInfo" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/AxisCalibrationInfoEntry" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/CompoundElement" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/MatchingCriteria_Base/ElementCount_Base" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_AppleGCController" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_AppleGCController_Base" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_AppleGCController_Base/Axis" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_AppleGCController_Base/Button" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_AppleGCController_Base/CompoundElement" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_AppleGCController_Base/Elements" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_AppleGCController_Base/MatchingCriteria" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_Custom/CustomCalculationSourceData" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_DirectInput" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_DirectInput_Base" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_DirectInput_Base/Axis" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_DirectInput_Base/Button" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_DirectInput_Base/Elements" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_Fallback" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_Fallback_Base" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_Fallback_Base/Axis" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_Fallback_Base/Button" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_Fallback_Base/CustomCalculationSourceData" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_Fallback_Base/Elements" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_Fallback_Base/MatchingCriteria" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_GameCore" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_GameCore_Base/Axis" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_GameCore_Base/Button" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_GameCore_Base/Elements" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_GameCore_Base/MatchingCriteria" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_InternalDriver" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_InternalDriver_Base/Axis" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_InternalDriver_Base/Button" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_InternalDriver_Base/Elements" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_InternalDriver_Base/MatchingCriteria" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_Linux" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_Linux_Base" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_Linux_Base/Axis" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_Linux_Base/Button" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_Linux_Base/Elements" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_Linux_Base/MatchingCriteria" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_Linux_Base/MatchingCriteria/ElementCount" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_NintendoSwitch" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_NintendoSwitch_Base/Axis" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_NintendoSwitch_Base/Button" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_NintendoSwitch_Base/Elements" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_NintendoSwitch_Base/MatchingCriteria" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_OSX" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_OSX_Base" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_OSX_Base/Axis" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_OSX_Base/Button" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_OSX_Base/Elements" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_OSX_Base/MatchingCriteria" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_OSX_Base/MatchingCriteria/ElementCount" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_PS4" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_PS4_Base/Axis" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_PS4_Base/Button" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_PS4_Base/Elements" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_PS4_Base/MatchingCriteria" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_PS5" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_PS5_Base/Axis" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_PS5_Base/Button" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_PS5_Base/Elements" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_PS5_Base/MatchingCriteria" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_RawInput" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_RawInput_Base" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_RawInput_Base/Axis" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_RawInput_Base/Button" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_RawInput_Base/Elements" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_RawOrDirectInput/CustomCalculationSourceData" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_RawOrDirectInput/MatchingCriteria" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_RawOrDirectInput/MatchingCriteria/ElementCount" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_SDL2" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_SDL2_Base/Axis" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_SDL2_Base/Button" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_SDL2_Base/Elements" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_SDL2_Base/MatchingCriteria" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_WebGL" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_WebGL_Base" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_WebGL_Base/Axis" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_WebGL_Base/Button" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_WebGL_Base/Elements" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_WebGL_Base/MatchingCriteria" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_WebGL_Base/MatchingCriteria/ClientInfo" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_WindowsUWP" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_WindowsUWP_Base" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_WindowsUWP_Base/Axis" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_WindowsUWP_Base/Button" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_WindowsUWP_Base/Elements" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_WindowsUWP_Base/MatchingCriteria" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_WindowsUWP_Base/MatchingCriteria/ElementCount" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_WindowsWGI" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_WindowsWGI_Base" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_WindowsWGI_Base/Axis" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_WindowsWGI_Base/Button" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_WindowsWGI_Base/Elements" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_WindowsWGI_Base/MatchingCriteria" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_XInput" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_XInput_Base/Axis" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_XInput_Base/Button" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_XInput_Base/Elements" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_XInput_Base/MatchingCriteria" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_XboxOne" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_XboxOne_Base/Axis" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_XboxOne_Base/Button" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_XboxOne_Base/Elements" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/Platform_XboxOne_Base/MatchingCriteria" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickMap/VidPid" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickTemplateMap/ElementIdentifierMap" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickTemplateMap/Entry" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Mapping.HardwareJoystickTemplateMap/SpecialElementEntry" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Player_Editor" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Player_Editor/ControllerMapEnablerSettings" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Player_Editor/ControllerMapLayoutManagerSettings" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.Player_Editor/Mapping" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Data.UserData" preserve="nothing" serialized="true" />
    <type fullname="Rewired.InputAction" preserve="nothing" serialized="true" />
    <type fullname="Rewired.InputActionCategory" preserve="nothing" serialized="true" />
    <type fullname="Rewired.InputBehavior" preserve="nothing" serialized="true" />
    <type fullname="Rewired.InputLayout" preserve="nothing" serialized="true" />
    <type fullname="Rewired.InputMapCategory" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="RewiredSwitch, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Rewired.Platforms.Switch.NintendoSwitchInputManager" preserve="all" />
    <type fullname="Rewired.Platforms.Switch.NintendoSwitchInputManager/DebugPadSettings_Internal" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Platforms.Switch.NintendoSwitchInputManager/NpadSettings_Internal" preserve="nothing" serialized="true" />
    <type fullname="Rewired.Platforms.Switch.NintendoSwitchInputManager/UserData" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="RTG, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="RTG.RTCameraBackground" preserve="all" />
    <type fullname="RTG.RTFocusCamera" preserve="all" />
    <type fullname="RTG.RTGApp" preserve="all" />
    <type fullname="RTG.RTGizmosEngine" preserve="all" />
    <type fullname="RTG.RTInputDevice" preserve="all" />
    <type fullname="RTG.RTScene" preserve="all" />
    <type fullname="RTG.RTSceneGrid" preserve="all" />
    <type fullname="RTG.RTUndoRedo" preserve="all" />
    <type fullname="RTG.CameraBackgroundSettings" preserve="nothing" serialized="true" />
    <type fullname="RTG.CameraFocusSettings" preserve="nothing" serialized="true" />
    <type fullname="RTG.CameraHotkeys" preserve="nothing" serialized="true" />
    <type fullname="RTG.CameraLookAroundSettings" preserve="nothing" serialized="true" />
    <type fullname="RTG.CameraMoveSettings" preserve="nothing" serialized="true" />
    <type fullname="RTG.CameraOrbitSettings" preserve="nothing" serialized="true" />
    <type fullname="RTG.CameraPanSettings" preserve="nothing" serialized="true" />
    <type fullname="RTG.CameraProjectionSwitchSettings" preserve="nothing" serialized="true" />
    <type fullname="RTG.CameraRotationSwitchSettings" preserve="nothing" serialized="true" />
    <type fullname="RTG.CameraSettings" preserve="nothing" serialized="true" />
    <type fullname="RTG.CameraZoomSettings" preserve="nothing" serialized="true" />
    <type fullname="RTG.EditorToolbar" preserve="nothing" serialized="true" />
    <type fullname="RTG.EditorToolbarTab" preserve="nothing" serialized="true" />
    <type fullname="RTG.GizmoCap2DLookAndFeel" preserve="nothing" serialized="true" />
    <type fullname="RTG.GizmoCap3DLookAndFeel" preserve="nothing" serialized="true" />
    <type fullname="RTG.GizmoEngineSettings" preserve="nothing" serialized="true" />
    <type fullname="RTG.GizmoLineSlider2DLookAndFeel" preserve="nothing" serialized="true" />
    <type fullname="RTG.GizmoLineSlider2DSettings" preserve="nothing" serialized="true" />
    <type fullname="RTG.GizmoLineSlider3DLookAndFeel" preserve="nothing" serialized="true" />
    <type fullname="RTG.GizmoLineSlider3DSettings" preserve="nothing" serialized="true" />
    <type fullname="RTG.GizmoObjectVertexSnapSettings" preserve="nothing" serialized="true" />
    <type fullname="RTG.GizmoPlaneSlider2DLookAndFeel" preserve="nothing" serialized="true" />
    <type fullname="RTG.GizmoPlaneSlider2DSettings" preserve="nothing" serialized="true" />
    <type fullname="RTG.GizmoPlaneSlider3DLookAndFeel" preserve="nothing" serialized="true" />
    <type fullname="RTG.GizmoPlaneSlider3DSettings" preserve="nothing" serialized="true" />
    <type fullname="RTG.GizmoRotationArc2DLookAndFeel" preserve="nothing" serialized="true" />
    <type fullname="RTG.GizmoRotationArc3DLookAndFeel" preserve="nothing" serialized="true" />
    <type fullname="RTG.GizmoScaleGuideLookAndFeel" preserve="nothing" serialized="true" />
    <type fullname="RTG.Hotkeys" preserve="nothing" serialized="true" />
    <type fullname="RTG.HotkeysStaticData" preserve="nothing" serialized="true" />
    <type fullname="RTG.MoveGizmoHotkeys" preserve="nothing" serialized="true" />
    <type fullname="RTG.MoveGizmoLookAndFeel2D" preserve="nothing" serialized="true" />
    <type fullname="RTG.MoveGizmoLookAndFeel3D" preserve="nothing" serialized="true" />
    <type fullname="RTG.MoveGizmoSettings2D" preserve="nothing" serialized="true" />
    <type fullname="RTG.MoveGizmoSettings3D" preserve="nothing" serialized="true" />
    <type fullname="RTG.ObjectTransformGizmoSettings" preserve="nothing" serialized="true" />
    <type fullname="RTG.RotationGizmoHotkeys" preserve="nothing" serialized="true" />
    <type fullname="RTG.RotationGizmoLookAndFeel3D" preserve="nothing" serialized="true" />
    <type fullname="RTG.RotationGizmoSettings3D" preserve="nothing" serialized="true" />
    <type fullname="RTG.ScaleGizmoHotkeys" preserve="nothing" serialized="true" />
    <type fullname="RTG.ScaleGizmoLookAndFeel3D" preserve="nothing" serialized="true" />
    <type fullname="RTG.ScaleGizmoSettings3D" preserve="nothing" serialized="true" />
    <type fullname="RTG.SceneGizmoLookAndFeel" preserve="nothing" serialized="true" />
    <type fullname="RTG.SceneGridHotkeys" preserve="nothing" serialized="true" />
    <type fullname="RTG.SceneSettings" preserve="nothing" serialized="true" />
    <type fullname="RTG.UniversalGizmoConfig" preserve="nothing" serialized="true" />
    <type fullname="RTG.UniversalGizmoHotkeys" preserve="nothing" serialized="true" />
    <type fullname="RTG.UniversalGizmoLookAndFeel2D" preserve="nothing" serialized="true" />
    <type fullname="RTG.UniversalGizmoLookAndFeel3D" preserve="nothing" serialized="true" />
    <type fullname="RTG.UniversalGizmoSettings2D" preserve="nothing" serialized="true" />
    <type fullname="RTG.UniversalGizmoSettings3D" preserve="nothing" serialized="true" />
    <type fullname="RTG.XZGridLookAndFeel" preserve="nothing" serialized="true" />
    <type fullname="RTG.XZGridSettings" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="ShapesRuntime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Shapes.Disc" preserve="all" />
    <type fullname="Shapes.Line" preserve="all" />
    <type fullname="Shapes.DashStyle" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UniTask, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Cysharp.Threading.Tasks.Triggers.AsyncDestroyTrigger" preserve="all" />
  </assembly>
  <assembly fullname="Unity.Addressables, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null" preserve="all">
    <type fullname="UnityEngine.AddressableAssets.Addressables" preserve="all" />
    <type fullname="UnityEngine.AddressableAssets.AssetReference" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Unity.Animation.Rigging, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Animations.Rigging.BoneRenderer" preserve="all" />
    <type fullname="UnityEngine.Animations.Rigging.Rig" preserve="all" />
    <type fullname="UnityEngine.Animations.Rigging.RigBuilder" preserve="all" />
    <type fullname="UnityEngine.Animations.Rigging.RigTransform" preserve="all" />
    <type fullname="UnityEngine.Animations.Rigging.RigLayer" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Unity.Localization, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Localization.Components.LocalizeStringEvent" preserve="all" />
    <type fullname="UnityEngine.Localization.Locale" preserve="all" />
    <type fullname="UnityEngine.Localization.Tables.SharedTableData" preserve="all" />
    <type fullname="UnityEngine.Localization.Tables.StringTable" preserve="all" />
    <type fullname="UnityEngine.Localization.LocaleIdentifier" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Localization.Metadata.MetadataCollection" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Localization.Tables.TableEntryData" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Localization.LocalizedString" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Localization.Tables.TableEntryReference" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Localization.Tables.TableReference" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Localization.Tables.DistributedUIDGenerator" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Localization.Tables.SharedTableData/SharedTableEntry" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Localization.Events.UnityEventString" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Unity.Postprocessing.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Rendering.PostProcessing.AmbientOcclusion" preserve="all" />
    <type fullname="UnityEngine.Rendering.PostProcessing.AutoExposure" preserve="all" />
    <type fullname="UnityEngine.Rendering.PostProcessing.Bloom" preserve="all" />
    <type fullname="UnityEngine.Rendering.PostProcessing.ChromaticAberration" preserve="all" />
    <type fullname="UnityEngine.Rendering.PostProcessing.ColorGrading" preserve="all" />
    <type fullname="UnityEngine.Rendering.PostProcessing.DepthOfField" preserve="all" />
    <type fullname="UnityEngine.Rendering.PostProcessing.Grain" preserve="all" />
    <type fullname="UnityEngine.Rendering.PostProcessing.MotionBlur" preserve="all" />
    <type fullname="UnityEngine.Rendering.PostProcessing.PostProcessLayer" preserve="all" />
    <type fullname="UnityEngine.Rendering.PostProcessing.PostProcessProfile" preserve="all" />
    <type fullname="UnityEngine.Rendering.PostProcessing.PostProcessResources" preserve="all" />
    <type fullname="UnityEngine.Rendering.PostProcessing.PostProcessVolume" preserve="all" />
    <type fullname="UnityEngine.Rendering.PostProcessing.ScreenSpaceReflections" preserve="all" />
    <type fullname="UnityEngine.Rendering.PostProcessing.Vignette" preserve="all" />
    <type fullname="UnityEngine.Rendering.PostProcessing.AmbientOcclusionModeParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.PostProcessing.AmbientOcclusionQualityParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.PostProcessing.BoolParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.PostProcessing.ColorParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.PostProcessing.EyeAdaptationParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.PostProcessing.FastApproximateAntialiasing" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.PostProcessing.FloatParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.PostProcessing.Fog" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.PostProcessing.GradingModeParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.PostProcessing.HistogramMonitor" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.PostProcessing.IntParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.PostProcessing.KernelSizeParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.PostProcessing.LightMeterMonitor" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.PostProcessing.PostProcessDebugLayer" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.PostProcessing.PostProcessDebugLayer/OverlaySettings" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.PostProcessing.PostProcessLayer/SerializedBundleRef" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.PostProcessing.PostProcessResources/ComputeShaders" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.PostProcessing.PostProcessResources/SMAALuts" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.PostProcessing.PostProcessResources/Shaders" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.PostProcessing.ScreenSpaceReflectionPresetParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.PostProcessing.ScreenSpaceReflectionResolutionParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.PostProcessing.Spline" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.PostProcessing.SplineParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.PostProcessing.SubpixelMorphologicalAntialiasing" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.PostProcessing.TemporalAntialiasing" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.PostProcessing.TextureParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.PostProcessing.TonemapperParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.PostProcessing.Vector2Parameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.PostProcessing.Vector4Parameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.PostProcessing.VectorscopeMonitor" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.PostProcessing.VignetteModeParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.PostProcessing.WaveformMonitor" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Unity.ResourceManager, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null" preserve="all">
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.AssetBundleProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.BundledAssetProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.InstanceProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.LegacyResourcesProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.SceneProvider" preserve="all" />
  </assembly>
  <assembly fullname="Unity.TextMeshPro, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="TMPro.TextMeshPro" preserve="all" />
    <type fullname="TMPro.TextMeshProUGUI" preserve="all" />
    <type fullname="TMPro.TMP_Dropdown" preserve="all" />
    <type fullname="TMPro.TMP_FontAsset" preserve="all" />
    <type fullname="TMPro.TMP_InputField" preserve="all" />
    <type fullname="TMPro.TMP_SpriteAsset" preserve="all" />
    <type fullname="TMPro.FaceInfo_Legacy" preserve="nothing" serialized="true" />
    <type fullname="TMPro.FontAssetCreationSettings" preserve="nothing" serialized="true" />
    <type fullname="TMPro.KerningTable" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_Character" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_FontFeatureTable" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_FontWeightPair" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_GlyphAdjustmentRecord" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_GlyphPairAdjustmentRecord" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_GlyphValueRecord" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_InputField/OnChangeEvent" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_InputField/SelectionEvent" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_InputField/SubmitEvent" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_InputField/TextSelectionEvent" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_InputField/TouchScreenKeyboardEvent" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_Sprite" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_SpriteCharacter" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_SpriteGlyph" preserve="nothing" serialized="true" />
    <type fullname="TMPro.VertexGradient" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_Dropdown/DropdownEvent" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_Dropdown/OptionData" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_Dropdown/OptionDataList" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEngine.AnimationModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.AnimationClip" preserve="all" />
    <type fullname="UnityEngine.Animations.ParentConstraint" preserve="all" />
    <type fullname="UnityEngine.Animator" preserve="all" />
    <type fullname="UnityEngine.Avatar" preserve="all" />
    <type fullname="UnityEngine.RuntimeAnimatorController" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.AudioModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.AudioListener" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Camera" preserve="all" />
    <type fullname="UnityEngine.ComputeShader" preserve="all" />
    <type fullname="UnityEngine.GameObject" preserve="all" />
    <type fullname="UnityEngine.Light" preserve="all" />
    <type fullname="UnityEngine.Material" preserve="all" />
    <type fullname="UnityEngine.Mesh" preserve="all" />
    <type fullname="UnityEngine.MeshFilter" preserve="all" />
    <type fullname="UnityEngine.MeshRenderer" preserve="all" />
    <type fullname="UnityEngine.MonoBehaviour" preserve="all" />
    <type fullname="UnityEngine.Object" preserve="all" />
    <type fullname="UnityEngine.RectTransform" preserve="all" />
    <type fullname="UnityEngine.RenderTexture" preserve="all" />
    <type fullname="UnityEngine.Shader" preserve="all" />
    <type fullname="UnityEngine.SkinnedMeshRenderer" preserve="all" />
    <type fullname="UnityEngine.Sprite" preserve="all" />
    <type fullname="UnityEngine.SpriteRenderer" preserve="all" />
    <type fullname="UnityEngine.TextAsset" preserve="all" />
    <type fullname="UnityEngine.Texture2D" preserve="all" />
    <type fullname="UnityEngine.Texture3D" preserve="all" />
    <type fullname="UnityEngine.Transform" preserve="all" />
    <type fullname="UnityEngine.U2D.SpriteAtlas" preserve="all" />
    <type fullname="UnityEngine.Hash128" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.PersistentCallGroup" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.UnityEvent`2[System.Single,UnityEngine.Vector3]" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.ArgumentCache" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.PersistentListenerMode" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.UnityEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.UnityEvent`1[System.Boolean]" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.UnityEvent`1[System.Int32]" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.UnityEvent`1[System.Single]" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.RectOffset" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEngine.ParticleSystemModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.ParticleSystem" preserve="all" />
    <type fullname="UnityEngine.ParticleSystemRenderer" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.PhysicsModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.BoxCollider" preserve="all" />
    <type fullname="UnityEngine.CapsuleCollider" preserve="all" />
    <type fullname="UnityEngine.ConfigurableJoint" preserve="all" />
    <type fullname="UnityEngine.PhysicMaterial" preserve="all" />
    <type fullname="UnityEngine.Rigidbody" preserve="all" />
    <type fullname="UnityEngine.SphereCollider" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.TextRenderingModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Font" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.UI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.EventSystems.EventSystem" preserve="all" />
    <type fullname="UnityEngine.EventSystems.EventTrigger" preserve="all" />
    <type fullname="UnityEngine.EventSystems.StandaloneInputModule" preserve="all" />
    <type fullname="UnityEngine.UI.AspectRatioFitter" preserve="all" />
    <type fullname="UnityEngine.UI.Button" preserve="all" />
    <type fullname="UnityEngine.UI.CanvasScaler" preserve="all" />
    <type fullname="UnityEngine.UI.ContentSizeFitter" preserve="all" />
    <type fullname="UnityEngine.UI.GraphicRaycaster" preserve="all" />
    <type fullname="UnityEngine.UI.GridLayoutGroup" preserve="all" />
    <type fullname="UnityEngine.UI.HorizontalLayoutGroup" preserve="all" />
    <type fullname="UnityEngine.UI.Image" preserve="all" />
    <type fullname="UnityEngine.UI.LayoutElement" preserve="all" />
    <type fullname="UnityEngine.UI.Mask" preserve="all" />
    <type fullname="UnityEngine.UI.RawImage" preserve="all" />
    <type fullname="UnityEngine.UI.RectMask2D" preserve="all" />
    <type fullname="UnityEngine.UI.Scrollbar" preserve="all" />
    <type fullname="UnityEngine.UI.ScrollRect" preserve="all" />
    <type fullname="UnityEngine.UI.Selectable" preserve="all" />
    <type fullname="UnityEngine.UI.Shadow" preserve="all" />
    <type fullname="UnityEngine.UI.Slider" preserve="all" />
    <type fullname="UnityEngine.UI.Toggle" preserve="all" />
    <type fullname="UnityEngine.UI.ToggleGroup" preserve="all" />
    <type fullname="UnityEngine.UI.VerticalLayoutGroup" preserve="all" />
    <type fullname="UnityEngine.EventSystems.EventTrigger/Entry" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.EventSystems.EventTrigger/TriggerEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.AnimationTriggers" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.ColorBlock" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.MaskableGraphic/CullStateChangedEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Navigation" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.ScrollRect/ScrollRectEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Scrollbar/ScrollEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Slider/SliderEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.SpriteState" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Button/ButtonClickedEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Toggle/ToggleEvent" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEngine.UIModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Canvas" preserve="all" />
    <type fullname="UnityEngine.CanvasGroup" preserve="all" />
    <type fullname="UnityEngine.CanvasRenderer" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.VideoModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Video.VideoClip" preserve="all" />
    <type fullname="UnityEngine.Video.VideoPlayer" preserve="all" />
  </assembly>
  <assembly fullname="Megagon.UGC">
    <type fullname="Megagon.UGC.ContentBundle/Info" preserve="nothing" serialized="true" />
    <type fullname="Megagon.UGC.ContentBundle/Version" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Megagon.Utility.Interface.Audio">
    <type fullname="Megagon.Utility.Interface.Audio.StableAudioEvent" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Megagon.Utility.Interface.Types.Common">
    <type fullname="Megagon.Utility.Interface.Types.FileInformation" preserve="nothing" serialized="true" />
    <type fullname="Megagon.Utility.Interface.Types.SaveLocation" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEngine.TextCoreFontEngineModule">
    <type fullname="UnityEngine.TextCore.FaceInfo" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.Glyph" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.GlyphMetrics" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.GlyphRect" preserve="nothing" serialized="true" />
  </assembly>
</linker>