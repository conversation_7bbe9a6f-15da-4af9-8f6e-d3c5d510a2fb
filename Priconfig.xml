<?xml version="1.0" encoding="utf-8"?>
<resources>
  <index root="\" startIndexAt="layout.resfiles">
    <default>
      <qualifier name="language" value="en-US" />
    </default>
    <indexer-config type="RESFILES" qualifierDelimiter="." />
  </index>
  <index root="\" startIndexAt="resources.resfiles">
    <default>
      <qualifier name="language" value="en-US" />
    </default>
    <indexer-config type="RESW" convertDotsToSlashes="true" />
    <indexer-config type="RESJSON" />
    <indexer-config type="RESFILES" qualifierDelimiter="." />
  </index>
  <index root="\" startIndexAt="pri.resfiles">
    <default>
      <qualifier name="language" value="en-US" />
    </default>
    <indexer-config type="PRI" />
    <indexer-config type="RESFILES" qualifierDelimiter="." />
  </index>
</resources>
